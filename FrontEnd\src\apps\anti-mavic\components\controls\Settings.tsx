import { useState } from "react";
import { useTranslation } from "react-i18next";
import * as Switch from "@radix-ui/react-switch";
import { Settings as SettingsIcon } from "lucide-react";

const Settings = () => {
  const { t } = useTranslation("antimavic");
  const [powerSavingMode, setPowerSavingMode] = useState(false);
  const [autoCooling, setAutoCooling] = useState(true);

  return (
    <div>
      <div className="flex items-center gap-1 mb-2">
        <SettingsIcon size={16} className="text-[--green-5]" />
        <h3 className="font-medium text-sm">
          {t("controls.settings.title")}
        </h3>
      </div>

      <div className="space-y-2">
        {/* Power Saving Mode Toggle */}
        <div className="flex items-center justify-between">
          <label
            htmlFor="power-saving-mode"
            className="text-xs"
          >
            {t("controls.settings.powderSavingMode")}
          </label>
          <Switch.Root
            id="power-saving-mode"
            checked={powerSavingMode}
            onCheckedChange={setPowerSavingMode}
            className={`w-[38px] h-[22px] rounded-full relative ${powerSavingMode ? "bg-[--green-5]" : "bg-[--gray-7]"} transition-colors`}
          >
            <Switch.Thumb className="block w-[18px] h-[18px] bg-white rounded-full transition-transform duration-100 will-change-transform translate-x-0.5 data-[state=checked]:translate-x-[17px]" />
          </Switch.Root>
        </div>

        {/* Auto Cooling Toggle */}
        <div className="flex items-center justify-between">
          <label htmlFor="auto-cooling" className="text-xs">
            {t("controls.settings.autoCooling")}
          </label>
          <Switch.Root
            id="auto-cooling"
            checked={autoCooling}
            onCheckedChange={setAutoCooling}
            className={`w-[38px] h-[22px] rounded-full relative ${autoCooling ? "bg-[--green-5]" : "bg-[--gray-7]"} transition-colors`}
          >
            <Switch.Thumb className="block w-[18px] h-[18px] bg-white rounded-full transition-transform duration-100 will-change-transform translate-x-0.5 data-[state=checked]:translate-x-[17px]" />
          </Switch.Root>
        </div>
      </div>
    </div>
  );
};

export default Settings;
