# Target and Device Creation/Editing Feature

This task involves implementing features for creating, editing, and deleting targets and devices in the map application.

## Features Implemented

### Forms

- Created forms for both targets and devices with the following fields:
  - **Target Form**: Name, Coordinates, Height (meters), Video Frequency, Telemetry Frequency
  - **Device Form**: Name, Coordinates, Type, Group, Distance (km), Coverage Angle, Azimuth, Vertical Angle Limit, Color, Mode, State

### Context Menus

- Added context menus that appear when right-clicking on devices or targets:
  - **Device Context Menu**: Displays name, Control, Edit, Move, Deactivate, Delete options
  - **Target Context Menu**: Displays name, Edit, Delete options

### Deletion Confirmation

- Implemented a confirmation dialog using Radix UI for deleting items
- Shows item name and asks for confirmation

### Integration with Map

- Updated the map component to include all new components
- Connected map context menu with forms for creating new items
- Connected marker clicks to context menus
- Implemented CRUD operations for both targets and devices

## Key Components

1. `DeviceForm.tsx` - Form for creating and editing devices
2. `TargetForm.tsx` - Form for creating and editing targets
3. `DeviceContextMenu.tsx` - Context menu for devices
4. `TargetContextMenu.tsx` - Context menu for targets
5. `DeleteConfirmationDialog.tsx` - Confirmation dialog for deletion
6. Updated `enhanced-map.tsx` to incorporate all these components

## Technical Details

### Forms

- Forms appear on the right side of the screen
- In creation mode: Show "Add" button
- In edit mode: Show "Edit" button
- Both forms have a "Cancel" button

### Context Menus

- Context menus are positioned at the cursor location
- They show the name of the item at the top
- They close when clicking elsewhere on the map

### Data Flow

- When creating a new item through the map context menu, the form is pre-populated with coordinates
- When editing an existing item, the form is pre-populated with all existing data
- Upon saving, the data is sent to the API and the UI is updated accordingly

### Styling

- All components use the app's dark/light theme system
- UI matches the designs provided in the screenshots
- Forms use appropriate input types for different data (sliders for numeric values with ranges)

## Translation

- Added translations for all new text in both English and Ukrainian
- Key translation groups:
  - Form fields and labels
  - Button text
  - Context menu options
  - Messages and confirmations

## Future Improvements

- Add form validation
- Implement drag-and-drop for moving devices/targets
- Add more detailed device/target type options
- Improve the UX for frequency input
- Add sorting and filtering options for devices and targets
