import { useState, useCallback } from "react";
import type { Device } from "../services/deviceService";
import type { Target } from "../services/targetService";
import type { MapContextMenuData } from "../components/context-menu";

export const useMapForms = (
  initialDevices: Device[] = [],
  initialTargets: Target[] = []
) => {
  // Form states
  const [deviceForm, setDeviceForm] = useState<{
    open: boolean;
    isEdit: boolean;
    device: Device | null;
  }>({
    open: false,
    isEdit: false,
    device: null,
  });

  const [targetForm, setTargetForm] = useState<{
    open: boolean;
    isEdit: boolean;
    target: Target | null;
  }>({
    open: false,
    isEdit: false,
    target: null,
  });

  // Handler for opening device form
  const openDeviceForm = useCallback(
    (
      coordinates: MapContextMenuData["coordinates"],
      devices: Device[] = initialDevices
    ) => {
      // Close target form if open
      setTargetForm((prev) => ({ ...prev, open: false }));

      setDeviceForm({
        open: true,
        isEdit: false,
        device: {
          lat: coordinates.lat,
          lng: coordinates.lng,
          name: `Device ${devices.length + 1}`,
        },
      });
    },
    [initialDevices]
  );

  // Handler for opening target form
  const openTargetForm = useCallback(
    (
      coordinates: MapContextMenuData["coordinates"],
      targets: Target[] = initialTargets
    ) => {
      // Close device form if open
      setDeviceForm((prev) => ({ ...prev, open: false }));

      setTargetForm({
        open: true,
        isEdit: false,
        target: {
          lat: coordinates.lat,
          lng: coordinates.lng,
          name: `Target ${targets.length + 1}`,
        },
      });
    },
    [initialTargets]
  );

  // Handler for editing device
  const editDevice = useCallback((device: Device) => {
    // Close target form if open
    setTargetForm((prev) => ({ ...prev, open: false }));

    setDeviceForm({
      open: true,
      isEdit: true,
      device,
    });
  }, []);

  // Handler for editing target
  const editTarget = useCallback((target: Target) => {
    // Close device form if open
    setDeviceForm((prev) => ({ ...prev, open: false }));

    setTargetForm({
      open: true,
      isEdit: true,
      target,
    });
  }, []);

  // Close device form
  const closeDeviceForm = useCallback(() => {
    setDeviceForm((prev) => ({ ...prev, open: false }));
  }, []);

  // Close target form
  const closeTargetForm = useCallback(() => {
    setTargetForm((prev) => ({ ...prev, open: false }));
  }, []);

  return {
    deviceForm,
    targetForm,
    openDeviceForm,
    openTargetForm,
    editDevice,
    editTarget,
    closeDeviceForm,
    closeTargetForm,
  };
};
