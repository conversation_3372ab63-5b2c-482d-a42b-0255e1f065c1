[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "awscrt>=0.23.10",
    "beanie>=1.29.0",
    "boto3>=1.37.11",
    "fastapi-jwt>=0.3.0",
    "fastapi[standard]>=0.115.6",
    "gmqtt>=0.7.0",
    "influxdb3-python>=0.12.0",
    "jinja2>=3.1.4",
    "loguru>=0.7.2",
    "motor>=3.7.0",
    "numpy>=2.2.0",
    "pandas>=2.2.3",
    "pydantic>=2.11",
    "polars>=1.26.0",
    "pyjwt>=2.10.1",
    "pymongo>=4.12.1",
    "pytest>=8.3.4",
    "pytest-asyncio>=0.25.2",
    "python-decouple>=3.8",
    "python-multipart>=0.0.19",
    "pytz>=2025.2",
    "redis[hiredis]>=4.6.0",
    "requests>=2.32.3",
    "types-awscrt>=0.23.10",
    "uvicorn>=0.32.1",
    "websockets>=14.1",
    "pydantic-settings>=2.9.1",
    "python-jose>=3.4.0",
    "bcrypt>=4.3.0",
    "passlib>=1.7.4",
    "rich>=13.9.4",
]

[tool.pytest.ini_options]
pythonpath = ["."]
asyncio_default_fixture_loop_scope = "session"
