from typing import List

from beanie import Document, Link
from pydantic import BaseModel, Field

from . import AmplifierDB


class Unit(BaseModel):
    name: str
    raspberry_id: str
    bandwidth_options: list[int] = [2, 5, 10, 20]
    ip: str | None = None
    devices: List[Link[AmplifierDB]] = []
    relay_leds: dict[str, str] | None = Field(
        None, examples=[{"0": "2400-2500 MHz", "1": "5725-5850 MHz"}]
    )


class UnitDB(Document, Unit):
    class Settings:
        name = "pro.units"
