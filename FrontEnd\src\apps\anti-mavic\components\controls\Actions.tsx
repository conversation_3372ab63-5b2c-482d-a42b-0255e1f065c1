import { useTranslation } from "react-i18next";
import { StopCircle, Camera } from "lucide-react";

const Actions = () => {
  const { t } = useTranslation("antimavic");

  const handleStopDevice = () => {
    console.log("Stop device clicked");
  };

  const handleScreenshot = () => {
    console.log("Screenshot clicked");
  };

  return (
    <div className="space-y-2">
      <button 
        className="w-full flex items-center justify-center gap-1.5 bg-[--green-5] text-white py-2 px-3 rounded-md text-sm"
        onClick={handleStopDevice}
      >
        <StopCircle size={16} />
        {t("controls.buttons.stopDevice")}
      </button>

      <button 
        className="w-full flex items-center justify-center gap-1.5 bg-[--gray-8] text-[--gray-1] py-2 px-3 rounded-md text-sm border border-[--gray-7]"
        onClick={handleScreenshot}
      >
        <Camera size={16} />
        {t("controls.buttons.screenshot")}
      </button>
    </div>
  );
};

export default Actions;
