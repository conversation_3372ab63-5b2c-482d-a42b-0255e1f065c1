from asyncio import TaskGroup
from typing import Literal

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from src.apps.auth.deps import UserDep
from src.apps.deps import ObjID_Dep, get_id_query
from src.conf.loader import get_motor_manager

router = APIRouter()

db = get_motor_manager()

device_group_col = db.pro.device_groups
station_group_col = db.pro.station_groups
station_col = db.pro.station


def check_db_obj(db_obj, user: UserDep):
    if db_obj is None:
        raise HTTPException(status_code=404, detail="Group not found")
    if db_obj.organization != user.organization:
        raise HTTPException(status_code=403, detail="Forbidden")


@router.get("/")
async def get_groups(station_id: int):
    q = {"station": station_id}
    groups = await device_group_col.find(q).to_list()
    return db.serialize(groups)


@router.post("/", status_code=201)
async def create_group(data: dict):
    result = await device_group_col.insert_one(data)
    return await db.get_by_id(device_group_col, result.inserted_id)


@router.put("/")
async def update_group(_: UserDep, data: dict):
    _id_obj = get_id_query(data.pop("id"))
    await device_group_col.update_one(_id_obj, {"$set": data})


@router.patch("/bandwidth_selector")
async def update_selector(_id: ObjID_Dep, bandwidth_options: list[int]):
    data = {"bandwidth_options": bandwidth_options}
    await device_group_col.update_one(_id, {"$set": data})


@router.delete("/", status_code=204)
async def delete_group(_id: ObjID_Dep):
    await device_group_col.delete_one(_id)


router_station = APIRouter(prefix="/station")


async def get_station_group_or_404(id_obj, user: UserDep):
    group = await db.get_by_id(station_group_col, id_obj)
    check_db_obj(group, user)
    return group


async def get_station_or_404(id_obj, user: UserDep):
    station = await db.get_by_id(db.pro.stations, id_obj)
    check_db_obj(station, user)
    return station


@router_station.get("/")
async def get_groups_station(user: UserDep):
    q = {"organization": user.organization}
    stations = await station_group_col.find(q).to_list()
    return db.serialize(stations)


@router_station.get("/list")
async def get_groups_station_one(user: UserDep):
    stations = await db.pro.stations.find().to_list()

    if user.is_developer():
        pass
        # available_stations_ids = [station._id for station in stations]
    elif user.is_admin():
        stations = filter(lambda g: g["organization"] == user.organization, stations)
    else:
        stations = user.stations
    return db.serialize(stations)


@router_station.get("/one")
async def get_groups_station_full(_id: ObjID_Dep, user: UserDep):
    return await get_station_group_or_404(_id, user)


@router_station.post("/", status_code=201)
async def create_group_station(user: UserDep, name: str):
    result = await db.pro.stations.insert_one({"name": name, "organization": user.organization})
    return await db.get_by_id(db.pro.stations, result.inserted_id)


class StationGroupSchema(BaseModel):
    _id: str
    station_ids: list[int]


@router_station.patch("/")
async def add_station_to_group(
    user: UserDep,
    s: StationGroupSchema,
    action: Literal["add", "remove"] = "add",
):
    _id = get_id_query(s._id)
    group = await get_station_group_or_404(_id, user)

    for station_id in s.station_ids:
        station_obj_id = get_id_query(station_id)
        station = await get_station_or_404(station_obj_id, user)

        # check if the station is already in the group
        if station["_id"] in group["stations"] and action == "add":
            return

        db_action = "$addToSet" if action == "add" else "$pull"
        await db.groups.update_one(
            _id, {db_action: {"stations": station_obj_id}}
        )


@router_station.delete("/", status_code=201)
async def delete_group_station(user: UserDep, _id: ObjID_Dep):
    await get_station_group_or_404(_id, user)
    await station_group_col.delete_one(_id)


@router_station.post("/stop_all", status_code=201)
async def stop_group(user: UserDep, _id: ObjID_Dep, controller):
    station_group = await get_station_group_or_404(_id, user)

    async with TaskGroup() as tg:
        for station_id in station_group["stations"]:
            tg.create_task(controller.stop_devices_for_station(station_id))


router.include_router(router_station)
