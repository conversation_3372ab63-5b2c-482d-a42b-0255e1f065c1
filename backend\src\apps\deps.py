from typing import Annotated, As<PERSON><PERSON>ener<PERSON>

import httpx
from bson import ObjectId
from bson.errors import InvalidId
from fastapi import Depends, HTTPException, Request
from src.conf.loader import get_motor_manager
from src.mqtt import MQTTManager
from src.services.redis import RedisManager
from src.services.tsdb import TSD<PERSON>lient


def get_id_query(_id: str) -> dict:
    try:
        if not isinstance(_id, ObjectId):
            _id = ObjectId(_id)
            return {"_id": _id}
    except InvalidId:
        raise HTTPException(status_code=400, detail="Invalid object ID format")


db = get_motor_manager()


# Generic function to get objects from app.state
def get_obj_from_app_state(name: str):
    def _get_obj(request: Request):
        return getattr(request.app.state, name)

    return _get_obj


async def get_httpx_client() -> AsyncGenerator[httpx.AsyncClient, None]:
    async with httpx.AsyncClient() as client:
        yield client


ObjID_Dep = Annotated[str, Depends(get_id_query)]
HTTPXClient = Annotated[httpx.AsyncClient, Depends(get_httpx_client)]
MQTTDeps = Annotated[MQTTManager, Depends(get_obj_from_app_state("mqtt"))]
RedisDeps = Annotated[RedisManager, Depends(get_obj_from_app_state("redis"))]
TSDBDeps = Annotated[TSDBClient, Depends(get_obj_from_app_state("ts_db"))]


# class RoleChecker:
#     def __init__(self, allowed_roles: list[str]) -> None:
#         self.allowed_roles = allowed_roles

#     def __call__(self, token: str = Depends(oauth2_scheme)):

#         user_data = get_user_data(token)

#         if user_data.get("user_role") in self.allowed_roles:
#             return True
#         return False

#         # raise credentials_exception

#     @classmethod
#     def developer(cls):
#         return cls(allowed_roles=["admin", "superuser", "developer"])

#     @classmethod
#     def admin(cls):
#         return cls(allowed_roles=["admin", "superuser", "developer"])

#     @classmethod
#     def user(cls):
#         return cls(allowed_roles=["user", "admin", "superuser", "developer"])

#     @classmethod
#     def guest(cls):
#         return cls(allowed_roles=["guest", "user", "admin", "superuser"])
