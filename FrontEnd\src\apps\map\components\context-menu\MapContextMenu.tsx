import { useState, useEffect, useRef, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Search, Ruler, MapPin, Target, Mountain, Copy } from "lucide-react";
import { useMapElevation } from "../../hooks";

export interface MapContextMenuData {
  coordinates: {
    lat: number;
    lng: number;
  };
  position: {
    x: number;
    y: number;
  };
}

export interface MapContextMenuProps {
  open: boolean;
  data: MapContextMenuData | null;
  onCreateDevice?: (coordinates: MapContextMenuData["coordinates"]) => void;
  onCreateTarget?: (coordinates: MapContextMenuData["coordinates"]) => void;
  onMeasuring?: () => void;
  onSearch?: () => void;
  onCopyCoordinates?: (coordinates: MapContextMenuData["coordinates"]) => void;
  onClose?: () => void;
}

export const MapContextMenu = ({
  open,
  data,
  onCreateDevice,
  onCreateTarget,
  onMeasuring,
  onSearch,
  onCopyCoordinates,
  onClose,
}: MapContextMenuProps) => {
  const { t } = useTranslation("map");
  const [elevation, setElevation] = useState<number | null>(null);
  const mapElevationService = useMapElevation();
  const menuRef = useRef<HTMLDivElement>(null);
  const [adjustedPosition, setAdjustedPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [visible, setVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  const fetchElevation = useCallback(async () => {
    if (!mapElevationService || !data?.coordinates) {
      return;
    }

    try {
      setElevation(null);
      const value = await mapElevationService.getElevation(data.coordinates);
      setElevation(value || null);
    } catch (e) {
      setElevation(null);
      console.error("Error fetching elevation:", e);
    }
  }, [data?.coordinates, mapElevationService]);

  // Handle open/close animations
  useEffect(() => {
    if (open) {
      setShouldRender(true);
      // Small delay to trigger the animation after the element is in the DOM
      requestAnimationFrame(() => {
        setVisible(true);
      });
    } else {
      setVisible(false);
      // Wait for animation to complete before removing from DOM
      const timer = setTimeout(() => {
        setShouldRender(false);
      }, 200); // Match this with the CSS transition duration
      return () => clearTimeout(timer);
    }
  }, [open]);

  useEffect(() => {
    if (!open) {
      // Reset position state when menu is closed
      setAdjustedPosition(null);
      setElevation(null);
      return;
    }

    if (open && data) {
      fetchElevation();

      // After component renders, check and adjust position if needed
      requestAnimationFrame(() => {
        if (menuRef.current) {
          const menuRect = menuRef.current.getBoundingClientRect();
          const windowWidth = window.innerWidth;
          const windowHeight = window.innerHeight;

          let x = data.position.x;
          let y = data.position.y;
          let needsAdjustment = false;

          // Check if menu extends beyond right edge of screen
          if (x + menuRect.width > windowWidth) {
            x = windowWidth - menuRect.width - 10; // 10px padding from edge
            needsAdjustment = true;
          }

          // Check if menu extends beyond bottom edge of screen
          if (y + menuRect.height > windowHeight) {
            y = windowHeight - menuRect.height - 10; // 10px padding from edge
            needsAdjustment = true;
          }

          // Only update position if adjustment is needed
          if (needsAdjustment) {
            setAdjustedPosition({ x, y });
          }
        }
      });
    }
  }, [open, data, fetchElevation]);

  // Handle clicks outside the menu
  useEffect(() => {
    const handleOutsideClick = () => {
      onClose?.();
    };

    if (open) {
      // Use setTimeout to avoid the current click triggering the handler
      setTimeout(() => {
        document.addEventListener("click", handleOutsideClick);
      }, 0);

      return () => {
        document.removeEventListener("click", handleOutsideClick);
      };
    }
  }, [open, onClose]);

  if (!shouldRender || !data) return null;

  // Use adjusted position only if available, otherwise use the original click position
  const menuPosition = adjustedPosition || data.position;

  return (
    <div
      ref={menuRef}
      className={`fixed z-50 transition-all duration-200 ease-in-out ${
        visible ? "opacity-100 scale-100" : "opacity-0 scale-95"
      }`}
      style={{
        left: `${menuPosition.x}px`,
        top: `${menuPosition.y}px`,
        transformOrigin: "top left",
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="min-w-[220px] bg-white dark:bg-[--gray-8] rounded-md overflow-hidden p-1 shadow-md select-none">
        <div className="flex items-center p-2 text-sm text-gray-700 dark:text-gray-300">
          <Mountain className="mr-2 h-4 w-4" />
          {t("elevation")}: {elevation ? `${elevation}${t("meters")}` : "--"}
        </div>

        <div className="h-px bg-gray-200 dark:bg-gray-700 my-1" />

        <button
          className="flex w-full items-center p-2 text-sm rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer font-medium text-blue-600 dark:text-blue-400"
          onClick={() => {
            onCreateDevice?.(data.coordinates);
            onClose?.();
          }}
        >
          <MapPin className="mr-2 h-4 w-4" />
          {t("create_device")}
        </button>

        <button
          className="flex w-full items-center p-2 text-sm rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer font-medium text-red-600 dark:text-red-400"
          onClick={() => {
            onCreateTarget?.(data.coordinates);
            onClose?.();
          }}
        >
          <Target className="mr-2 h-4 w-4" />
          {t("create_target")}
        </button>

        <div className="h-px bg-gray-200 dark:bg-gray-700 my-1" />

        <button
          className="flex w-full items-center p-2 text-sm rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          onClick={() => {
            onSearch?.();
            onClose?.();
          }}
        >
          <Search className="mr-2 h-4 w-4" />
          {t("search")}
        </button>

        <button
          className="flex w-full items-center p-2 text-sm rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          onClick={() => {
            onMeasuring?.();
            onClose?.();
          }}
        >
          <Ruler className="mr-2 h-4 w-4" />
          {t("distance")}
        </button>

        {onCopyCoordinates && (
          <button
            className="flex w-full items-center p-2 text-sm rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
            onClick={() => {
              onCopyCoordinates(data.coordinates);
              onClose?.();
            }}
          >
            <Copy className="mr-2 h-4 w-4" />
            {t("copy_coordinates")}
          </button>
        )}
      </div>
    </div>
  );
};
