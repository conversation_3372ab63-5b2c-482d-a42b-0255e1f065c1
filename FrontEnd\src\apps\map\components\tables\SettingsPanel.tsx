import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X, Map, Layers, Globe, Languages, Database, MapPin } from "lucide-react";
import * as Accordion from "@radix-ui/react-accordion";
import { NavItem } from "../navbar/NavItem";
import { MapTypeSelector } from "../navbar/MapTypeSelector";
import { CoordinateSelector } from "../navbar/CoordinateSelector";
import { LanguageSelector } from "../navbar/LanguageSelector";
import { DataControls } from "../navbar/DataControls";
import { ThemeToggle } from "../navbar/ThemeToggle";
import { ConflictLinesToggle } from "../navbar/ConflictLinesToggle";
import { DeviceCoverageToggle } from "../navbar/DeviceCoverageToggle";
import { useResizable } from "../../hooks/useResizable";
import { ResizeHandle } from "./ResizeHandle";

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onWidthChange?: (width: number) => void;
  // Map settings props
  mapType: "roadmap" | "satellite";
  onMapTypeChange: (type: "roadmap" | "satellite") => void;
  coordinateSystem: "WGS84" | "MGRS";
  onCoordinateSystemChange: (system: "WGS84" | "MGRS") => void;
  onLanguageChange: (language: "en" | "uk") => void;
  isDarkMode: boolean;
  onThemeToggle: () => void;
  // Conflict lines props
  showConflictLines: boolean;
  onConflictLinesToggle: (visible: boolean) => void;
  // Device coverage props
  showDeviceCoverage: boolean;
  onDeviceCoverageToggle: (visible: boolean) => void;
  onExport: () => void;
  onImport: () => void;
  onRemoveDatabase: () => void;
  className?: string;
}

export const SettingsPanel = ({
  isOpen,
  onClose,
  onWidthChange,
  mapType,
  onMapTypeChange,
  coordinateSystem,
  onCoordinateSystemChange,
  onLanguageChange,
  isDarkMode,
  onThemeToggle,
  showConflictLines,
  onConflictLinesToggle,
  showDeviceCoverage,
  onDeviceCoverageToggle,
  onExport,
  onImport,
  onRemoveDatabase,
  className = "",
}: SettingsPanelProps) => {
  const { t } = useTranslation("map");

  // Panel resizing
  const { width: panelWidth, handleMouseDown: handlePanelResize } = useResizable({
    initialWidth: 350,
    minWidth: 280,
    maxWidth: 500,
  });

  // Local state for controlled components
  const [localMapType, setLocalMapType] = useState(mapType);
  const [localCoordinateSystem, setLocalCoordinateSystem] = useState(coordinateSystem);

  const handleMapTypeChange = (type: "roadmap" | "satellite") => {
    setLocalMapType(type);
    onMapTypeChange(type);
  };

  const handleCoordinateSystemChange = (system: "WGS84" | "MGRS") => {
    setLocalCoordinateSystem(system);
    onCoordinateSystemChange(system);
  };

  // Notify parent about width changes
  useEffect(() => {
    if (onWidthChange && isOpen) {
      onWidthChange(panelWidth);
    }
  }, [panelWidth, isOpen, onWidthChange]);

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed left-64 top-0 h-full bg-white dark:bg-[--military-dark] shadow-lg z-30 border-r border-gray-200 dark:border-[--military-accent] flex ${className}`}
      style={{ width: panelWidth }}
    >
      {/* Main panel content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-[--military-accent]">
          <h2 className="text-lg font-medium text-gray-700 dark:text-[--sand-light]">
            {t("settings")}
          </h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-[--military-light] text-gray-700 dark:text-[--sand-light] transition-colors duration-150"
            aria-label={t("close")}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Settings Content */}
        <div className="flex-1 overflow-y-auto">
          <Accordion.Root
            type="multiple"
            defaultValue={["map", "theme", "coordinates", "language", "conflict", "coverage", "data"]}
            className="w-full"
          >
            <NavItem
              icon={<Map className="h-5 w-5" />}
              label={t("map")}
              value="map"
            >
              <MapTypeSelector value={localMapType} onChange={handleMapTypeChange} />
            </NavItem>

            <NavItem
              icon={<Layers className="h-5 w-5" />}
              label={t("theme")}
              value="theme"
            >
              <ThemeToggle isDarkMode={isDarkMode} onToggle={onThemeToggle} />
            </NavItem>

            <NavItem
              icon={<Globe className="h-5 w-5" />}
              label={t("coordinates")}
              value="coordinates"
            >
              <CoordinateSelector
                value={localCoordinateSystem}
                onChange={handleCoordinateSystemChange}
              />
            </NavItem>

            <NavItem
              icon={<Languages className="h-5 w-5" />}
              label={t("language")}
              value="language"
            >
              <LanguageSelector />
            </NavItem>

            <NavItem
              icon={<MapPin className="h-5 w-5" />}
              label={t("conflict_lines")}
              value="conflict"
            >
              <ConflictLinesToggle
                isVisible={showConflictLines}
                onToggle={onConflictLinesToggle}
              />
            </NavItem>

            <NavItem
              icon={<Layers className="h-5 w-5" />}
              label={t("device_coverage")}
              value="coverage"
            >
              <DeviceCoverageToggle
                isVisible={showDeviceCoverage}
                onToggle={onDeviceCoverageToggle}
              />
            </NavItem>

            <NavItem
              icon={<Database className="h-5 w-5" />}
              label={t("data")}
              value="data"
            >
              <DataControls
                onExport={onExport}
                onImport={onImport}
                onRemoveDatabase={onRemoveDatabase}
              />
            </NavItem>
          </Accordion.Root>
        </div>
      </div>

      {/* Resize handle for panel */}
      <ResizeHandle onMouseDown={handlePanelResize} />
    </div>
  );
}; 