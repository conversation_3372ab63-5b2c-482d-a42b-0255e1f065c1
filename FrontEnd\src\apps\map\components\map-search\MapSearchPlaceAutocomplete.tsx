import { useCallback, useMemo, useRef, useState, useEffect } from "react";
import _debounce from "lodash/debounce";
import { useMapsLibrary } from "@vis.gl/react-google-maps";
import { isArray } from "lodash";

// Interfaces
export interface MapSearchPlaceAutocompleteProps {
  label?: string;
  placeholder?: string;
  onChange?: (value: google.maps.places.AutocompletePrediction) => void;
  className?: string;
}

// Component
export const MapSearchPlaceAutocomplete = (
  props: MapSearchPlaceAutocompleteProps
) => {
  const { label, placeholder, onChange, className = "" } = props;
  const [options, setOptions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [inputValue, setInputValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const placesLib = useMapsLibrary("places");
  const placeAutocompleteServiceRef =
    useRef<google.maps.places.AutocompleteService | null>(null);

  // Initialize AutocompleteService when places library is available
  useEffect(() => {
    if (placesLib) {
      try {
        placeAutocompleteServiceRef.current =
          new placesLib.AutocompleteService();
      } catch (error) {
        console.error("Failed to create AutocompleteService:", error);
      }
    }
  }, [placesLib]);

  const fetchPlaces = useMemo(() => {
    return _debounce(async (query: string) => {
      if (query.trim().length < 2) {
        setOptions([]);
        setIsOpen(false);
        return;
      }

      if (!placesLib) {
        console.error("Places library not loaded");
        return;
      }

      if (!placeAutocompleteServiceRef.current) {
        console.error("AutocompleteService not available");
        return;
      }

      try {
        setLoading(true);

        console.log("Searching places for:", query);

        // Use async/await approach like the old working implementation
        const result =
          await placeAutocompleteServiceRef.current.getPlacePredictions({
            input: query,
            componentRestrictions: {
              country: ["ua"],
            },
            types: ["political", "locality"],
          });

        console.log("Places API response:", {
          count: result.predictions?.length || 0,
        });

        if (isArray(result.predictions)) {
          console.log("Places found:", result.predictions.length);
          setOptions(result.predictions);
          setIsOpen(result.predictions.length > 0);
        } else {
          console.log("No predictions in result");
          setOptions([]);
          setIsOpen(false);
        }
      } catch (e) {
        console.error("Error in fetchPlaces:", e);
        setOptions([]);
        setIsOpen(false);
      } finally {
        setLoading(false);
      }
    }, 300);
  }, [placesLib]);

  const handleInputChange = useCallback(
    (ev: React.ChangeEvent<HTMLInputElement>) => {
      const value = ev.target.value;
      setInputValue(value);

      if (value.trim().length >= 2) {
        fetchPlaces(value);
      } else {
        setOptions([]);
        setIsOpen(false);
      }
    },
    [fetchPlaces]
  );

  const handleOptionSelect = useCallback(
    (option: google.maps.places.AutocompletePrediction) => {
      setInputValue(option.description);
      setIsOpen(false);
      setOptions([]);

      if (onChange) {
        onChange(option);
      }
    },
    [onChange]
  );

  const handleInputFocus = useCallback(() => {
    if (options.length > 0) {
      setIsOpen(true);
    }
  }, [options.length]);

  const handleInputBlur = useCallback(() => {
    // Delay closing to allow option selection
    setTimeout(() => setIsOpen(false), 200);
  }, []);

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-[--sand-light] mb-2">
          {label}
        </label>
      )}

      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          className="w-full px-3 py-2 border border-gray-300 dark:border-[--military-accent] rounded-md shadow-sm bg-white dark:bg-[--military-dark] text-gray-900 dark:text-[--sand-light] placeholder-gray-500 dark:placeholder-[--sand-light]/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />

        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {isOpen && options.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-[--military-dark] border border-gray-300 dark:border-[--military-accent] rounded-md shadow-lg max-h-60 overflow-auto">
          {options.map((option) => (
            <div
              key={option.place_id}
              className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-[--military-light] border-b border-gray-100 dark:border-[--military-accent] last:border-b-0"
              onClick={() => handleOptionSelect(option)}
            >
              <div className="text-sm font-medium text-gray-900 dark:text-[--sand-light]">
                {option.structured_formatting.main_text}
              </div>
              {option.structured_formatting.secondary_text && (
                <div className="text-xs text-gray-500 dark:text-[--sand-light]/70">
                  {option.structured_formatting.secondary_text}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
