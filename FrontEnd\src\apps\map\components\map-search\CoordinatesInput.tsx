import { useState, useCallback } from "react";

export interface CoordinatesInputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export const CoordinatesInput = ({
  label,
  placeholder,
  value,
  onChange,
  className = "",
}: CoordinatesInputProps) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  return (
    <div className={`${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-[--sand-light] mb-2">
          {label}
        </label>
      )}

      <div className="relative">
        <input
          type="text"
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm bg-white dark:bg-[--military-dark] text-gray-900 dark:text-[--sand-light] placeholder-gray-500 dark:placeholder-[--sand-light]/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            isFocused
              ? "border-blue-500 dark:border-blue-500"
              : "border-gray-300 dark:border-[--military-accent]"
          }`}
        />

        {/* Hint text */}
        <div className="mt-1 text-xs text-gray-500 dark:text-[--sand-light]/70">
          Format: 49.0139, 31.2858 or 49°00'50.0"N 31°17'08.9"E
        </div>
      </div>
    </div>
  );
};
