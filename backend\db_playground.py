import asyncio

# from rich import print
from src.apps.auth import models as auth_models
from src.apps.pro import models as pro_models


async def init():
    await pro_models.init_beanie_models()
    await auth_models.init_beanie_models()


async def main():
    await init()

    # group_id = "1"
    # result = await pro_models.GroupDB.find(fetch_links=True).to_list()
    # group_result = await pro_models.GroupDB.find().to_list()

    # result_no_link = await pro_models.StationDB.find().to_list()
    result = await pro_models.StationDB.find_one({"name": "string"}, fetch_links=True)

    # print(group_result)
    print("------------")
    print(result)


if __name__ == "__main__":
    asyncio.run(main())
