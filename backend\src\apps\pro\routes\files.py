from fastapi import APIRouter, UploadFile
from src.conf.loader import get_motor_manager
from src.conf.settings import dirs

router = APIRouter()

db = get_motor_manager()

collection = db.map.devices


@router.get("/list")
async def get_presets():
    files = await collection.find().to_list()
    return files


@router.post("/upload/{file_name}")
async def upload_file(file_name: str, file: UploadFile):
    # Create a safe file path
    file_path = dirs.signal_files / f"{file_name}.{file.filename.split(".")[-1]}"

    # Save the uploaded file to the server
    with open(file_path, "wb") as f:
        # Read the file's content and write it to the file
        content = await file.read()
        f.write(content)

    await collection.insert_one({"name": file_name, "path": str(file_path)})

    return {"filename": file.filename, "path": file_path, "name": file_name}
