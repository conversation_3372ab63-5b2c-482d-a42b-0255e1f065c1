import importlib
import os
from pathlib import Path

from fastapi import APIRouter


def load_routes(router: APIRouter):
    # Path to the current directory (routes)
    routes_path = Path(__file__).parent
    app_name = Path(routes_path).parent.name
    print(routes_path, "routes_path")
    # Loop through all Python files in the routes directory
    for filename in os.listdir(routes_path):
        if filename.endswith(".py") and filename != "__init__.py":
            # Get the module name from the file (without .py)
            module_name = filename[:-3]
            # Construct the full file path
            file_path = routes_path / filename
            # Convert file path to module path (e.g., src.apps.module.routes.file)
            module_path = ".".join(
                file_path.relative_to(Path.cwd()).with_suffix("").parts
            )

            print(f"[{app_name}] Loading router from {module_name}...", end=" ")
            module = importlib.import_module(module_path)
            # Attach the router to the main_router if it exists
            if hasattr(module, "router"):
                router.include_router(
                    module.router,
                    prefix=f"/{app_name}/{module_name}",
                    tags=[f"[{app_name}] {module_name}"],
                )
                print(f"Loaded!")


def make_main_router(prefix: str = "", tags: list[str] = []) -> APIRouter:
    router = APIRouter(prefix=prefix, tags=tags)
    load_routes(router)
    return router