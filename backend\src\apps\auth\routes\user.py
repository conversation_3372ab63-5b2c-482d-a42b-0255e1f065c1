from fastapi import APIRouter, HTTPException, Response, Security
from fastapi_jwt import JwtAuthorizationCredentials
from src.apps.auth.utils.password import get_password_hash

from ..deps import UserDep
from ..jwt import access_security
from ..models import OrgDB, User, UserDB

router = APIRouter()


@router.get("/me", response_model=UserDB)
async def get_user(user: UserDep):
    """Return the current user."""
    return user


@router.post("/")
async def create_user(new_user: User) -> Response:
    """Create user."""
    user = await UserDB.by_username(new_user.username)
    if user is not None:
        raise HTTPException(409, "User with that username already exists")
    org = await OrgDB.get(new_user.org)
    if org is None:
        raise HTTPException(404, "Organization not found")
    new_user.password = get_password_hash(new_user.password)
    user = UserDB(**new_user.model_dump())
    await user.create()
    return user


@router.delete("/")
async def delete_user(
    auth: JwtAuthorizationCredentials = Security(access_security),
) -> Response:
    """Delete current user."""
    await UserDB.find_one(UserDB.username == auth.subject["username"]).delete()
    return Response(status_code=204)
