import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Search } from "lucide-react";
import type { Device } from "../../../services/deviceService";

interface DevicesTableProps {
  devices: Device[];
  className?: string;
}

export const DevicesTable = ({ devices, className = "" }: DevicesTableProps) => {
  const { t } = useTranslation("map");
  const [searchQuery, setSearchQuery] = useState("");

  const filteredDevices = useMemo(() => {
    if (!searchQuery.trim()) return devices;
    
    const query = searchQuery.toLowerCase();
    return devices.filter(device =>
      device.name?.toLowerCase().includes(query) ||
      device.type?.toLowerCase().includes(query) ||
      device.azimuth?.toString().includes(query)
    );
  }, [devices, searchQuery]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className={`bg-[--gray-9] dark:bg-[--gray-8] border border-[--gray-3] dark:border-[--gray-7] rounded-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-[--gray-3] dark:border-[--gray-7]">
        <h3 className="text-sm font-medium text-[--gray-1] dark:text-[--gray-1] mb-3">
          Пристрої
        </h3>
        
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[--gray-5] dark:text-[--gray-5]" />
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="Пошук"
            className="w-full pl-10 pr-3 py-2 bg-[--gray-8] dark:bg-[--gray-9] border border-[--gray-3] dark:border-[--gray-6] rounded-md text-[--gray-1] dark:text-[--gray-1] placeholder-[--gray-5] dark:placeholder-[--gray-5] text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Table */}
      <div className="min-h-[200px]">
        {/* Table Header */}
        <div className="grid grid-cols-3 px-4 py-3 border-b border-[--gray-3] dark:border-[--gray-7] bg-[--gray-8] dark:bg-[--gray-8]">
          <div className="text-xs font-medium text-[--gray-2] dark:text-[--gray-3] uppercase tracking-wider">
            Група / Назва
          </div>
          <div className="text-xs font-medium text-[--gray-2] dark:text-[--gray-3] uppercase tracking-wider">
            Азимут
          </div>
          <div className="text-xs font-medium text-[--gray-2] dark:text-[--gray-3] uppercase tracking-wider">
            Ціль
          </div>
        </div>

        {/* Table Body */}
        <div className="max-h-[400px] overflow-y-auto">
          {filteredDevices.length > 0 ? (
            filteredDevices.map((device, index) => (
              <div
                key={device._id || index}
                className="grid grid-cols-3 px-4 py-3 border-b border-[--gray-3] dark:border-[--gray-7] hover:bg-[--gray-8] dark:hover:bg-[--gray-7] transition-colors duration-150 cursor-pointer"
              >
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
                    {device.name || "Без назви"}
                  </span>
                  <span className="text-xs text-[--gray-4] dark:text-[--gray-4]">
                    {device.type || "Невідомий тип"}
                  </span>
                </div>
                <div className="text-sm text-[--gray-1] dark:text-[--gray-1]">
                  {device.azimuth !== undefined ? `${device.azimuth}°` : "—"}
                </div>
                <div className="text-sm text-[--gray-1] dark:text-[--gray-1]">
                  —
                </div>
              </div>
            ))
          ) : (
            <div className="flex items-center justify-center py-12">
              <span className="text-sm text-[--gray-4] dark:text-[--gray-4]">
                Немає рядків
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 