from contextlib import asynccontextmanager

from beanie import init_beanie
from fastapi import APIRouter, FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.apps import auth, map, no_mavic, pro
from src.apps.auth import models as auth_models
from src.apps.pro import models as pro_models
from src.apps.pro.controller import Controller
from src.conf.loader import get_motor_manager
from src.conf.settings import dirs, settings
from src.mqtt import MQTTManager
from src.services.redis import RedisManager
from src.services.tsdb import TSDBClient
from starlette.middleware import Middleware
from starlette.routing import Mount
from starlette.staticfiles import StaticFiles

redis = RedisManager()
ts_db = TSDBClient()

ecoflow = True


@asynccontextmanager
async def lifespan(_: FastAPI):
    await mqtt.connect()
    # await trigger_raspberry_update(mqtt)

    models = pro_models.get_models() + auth_models.get_models()
    await init_beanie(get_motor_manager().apps_db, document_models=models)
    print("Connected to MongoDB")

    controller = Controller(mqtt, redis, ts_db)
    controller.start()

    app.state.controller = controller
    app.state.redis = redis
    app.state.ts_db = ts_db
    app.state.ecoflow = ecoflow

    yield
    await mqtt.disconnect()


main_router = APIRouter()
main_router.include_router(auth.router)
main_router.include_router(map.router)
main_router.include_router(pro.router)
main_router.include_router(no_mavic.router)

# Define CORS settings
cors_middleware = Middleware(
    CORSMiddleware,
    allow_origins=settings.backend_cors_origins,
    allow_credentials=True,  # Allow cookies or authentication headers
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

debug = True if settings.env_type == "dev" else False


mqtt = MQTTManager(
    host=settings.mqtt_broker_host,
    port=settings.mqtt_broker_port,
    username=settings.mqtt_username,
    password=settings.mqtt_password,
    prefix=settings.mqtt_prefix,
)
# The main app
app = FastAPI(
    routes=[
        Mount("/logs/", StaticFiles(directory=dirs.logs)),
    ],
    middleware=[cors_middleware],
    debug=debug,
    lifespan=lifespan,
    openapi_url="/openapi.json",
)


app.include_router(main_router, prefix="/api")


@app.get("/get_existing_logs")
async def logs() -> list[str]:
    return [log.name for log in dirs.logs.iterdir() if log.is_file()]


# Use only one instance of the MQTTManager
app.state.mqtt = mqtt
