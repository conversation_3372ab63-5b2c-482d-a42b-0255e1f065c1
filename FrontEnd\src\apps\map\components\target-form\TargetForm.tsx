import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X } from "lucide-react";
import type { Target } from "../../services/targetService";

interface TargetFormProps {
  isOpen: boolean;
  initialData?: Target | null;
  isEdit?: boolean;
  onClose: () => void;
  onSave: (target: Omit<Target, "id">) => void;
}

export const TargetForm = ({
  isOpen,
  initialData,
  isEdit = false,
  onClose,
  onSave,
}: TargetFormProps) => {
  const { t } = useTranslation("map");
  const [formData, setFormData] = useState<Omit<Target, "id">>({
    lat: 0,
    lng: 0,
    name: "",
    height: 150,
    videoFrequency: "1040; 1500",
    telemetryFrequency: "700-800; 868-915",
  });

  // Add coordinates string state for display
  const [coordinatesStr, setCoordinatesStr] = useState("");

  // Initialize form with initial data if provided
  useEffect(() => {
    if (initialData) {
      setFormData({
        lat: initialData.lat,
        lng: initialData.lng,
        name: initialData.name || "",
        height: initialData.height || 150,
        videoFrequency: initialData.videoFrequency || "1040; 1500",
        telemetryFrequency:
          initialData.telemetryFrequency || "700-800; 868-915",
      });
      // Set coordinates string
      setCoordinatesStr(`${initialData.lat}, ${initialData.lng}`);
    }
  }, [initialData]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (name === "coordinates") {
      setCoordinatesStr(value);
      // Parse coordinates from comma-separated string
      const coords = value.split(",").map(coord => coord.trim());
      if (coords.length === 2) {
        const lat = parseFloat(coords[0]) || 0;
        const lng = parseFloat(coords[1]) || 0;
        setFormData(prev => ({ ...prev, lat, lng }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Ensure lat and lng are numbers before saving
    const formattedData = {
      ...formData,
      lat:
        typeof formData.lat === "string"
          ? parseFloat(formData.lat) || 0
          : formData.lat,
      lng:
        typeof formData.lng === "string"
          ? parseFloat(formData.lng) || 0
          : formData.lng,
      height:
        typeof formData.height === "string"
          ? parseFloat(formData.height) || 0
          : formData.height,
    };

    onSave(formattedData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-[380px] bg-[--gray-9] dark:bg-[--gray-8] shadow-lg z-20 overflow-y-auto">
      <div className="flex items-center justify-between p-4 border-b border-[--gray-3] dark:border-[--gray-7]">
        <h2 className="text-lg font-medium text-[--gray-1] dark:text-[--gray-1]">
          {isEdit ? t("edit_target") : t("new_target")}
        </h2>
        <button
          className="p-1 rounded-full hover:bg-[--gray-8] dark:hover:bg-[--gray-7] text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
          onClick={onClose}
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("name")}
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
            placeholder={t("enter_name")}
          />
        </div>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("type")}
          </label>
          <select
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
            defaultValue="444"
          >
            <option value="444">444</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("group")}
          </label>
          <select
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
            defaultValue="Group"
          >
            <option value="Group">Group</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("coordinates")}
          </label>
          <input
            type="text"
            name="coordinates"
            value={coordinatesStr}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
            placeholder="Latitude, Longitude"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("devices")}
          </label>
          <select className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]">
            <option value="">{t("select_devices")}</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("height")} (m)
          </label>
          <input
            type="number"
            name="height"
            value={formData.height}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
          />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
              {t("video")}
            </label>
            <span className="text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
              {t("band")}
            </span>
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              name="videoFrequency"
              value={formData.videoFrequency}
              onChange={handleChange}
              className="w-3/4 p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
              placeholder={t("enter_frequencies")}
            />
            <input
              type="text"
              className="w-1/4 p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
              placeholder="/"
            />
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
              {t("telemetry")}
            </label>
            <span className="text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
              {t("band")}
            </span>
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              name="telemetryFrequency"
              value={formData.telemetryFrequency}
              onChange={handleChange}
              className="w-3/4 p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
              placeholder={t("enter_frequencies")}
            />
            <input
              type="text"
              className="w-1/4 p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
              placeholder="/"
            />
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md text-[--gray-1] dark:text-[--gray-1] hover:bg-[--gray-8] dark:hover:bg-[--gray-7] transition-colors duration-200"
          >
            {t("cancel")}
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[--red-6] hover:bg-[--red-5] dark:bg-[--red-5] dark:hover:bg-[--red-6] text-white rounded-md transition-colors duration-200"
          >
            {isEdit ? t("edit") : t("add")}
          </button>
        </div>
      </form>
    </div>
  );
};
