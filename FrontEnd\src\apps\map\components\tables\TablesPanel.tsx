import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ChevronDown, ChevronUp } from "lucide-react";
import type { Device } from "../../services/deviceService";
import type { Target } from "../../services/targetService";
import { DevicesTable } from "./devices/DevicesTable";
import { TargetsTable } from "./targets/TargetsTable";

interface TablesPanelProps {
  devices: Device[];
  targets: Target[];
  className?: string;
}

export const TablesPanel = ({ devices, targets, className = "" }: TablesPanelProps) => {
  const { t } = useTranslation("map");
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className={`bg-[--gray-9] dark:bg-[--gray-8] border border-[--gray-3] dark:border-[--gray-7] rounded-lg ${className}`}>
      {/* Panel Header */}
      <div className="flex items-center justify-between p-4 border-b border-[--gray-3] dark:border-[--gray-7]">
        <h2 className="text-lg font-medium text-[--gray-1] dark:text-[--gray-1]">
          Таблиці
        </h2>
        <button
          onClick={handleToggleCollapse}
          className="p-1 rounded-full hover:bg-[--gray-8] dark:hover:bg-[--gray-7] text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
          aria-label={isCollapsed ? "Розгорнути" : "Згорнути"}
        >
          {isCollapsed ? (
            <ChevronDown className="h-5 w-5" />
          ) : (
            <ChevronUp className="h-5 w-5" />
          )}
        </button>
      </div>

      {/* Panel Content */}
      {!isCollapsed && (
        <div className="p-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Devices Table */}
            <DevicesTable devices={devices} />
            
            {/* Targets Table */}
            <TargetsTable targets={targets} />
          </div>
        </div>
      )}
    </div>
  );
}; 