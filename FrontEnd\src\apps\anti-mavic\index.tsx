import { useRef } from "react";
import CameraView from "./components/CameraView";
import type { CameraViewRef } from "./components/CameraView";
import SidePanel from "./components/SidePanel";
import BottomPanel from "./components/BottomPanel";
import {
  useVideoAspectRatio,
  getLayoutCSSProperties,
} from "./utils/useVideoAspectRatio";

const AntiMavicApp = () => {
  const cameraViewRef = useRef<CameraViewRef>(null);

  // Get camera refs and active view from CameraView component
  const mainCameraRef = cameraViewRef.current?.getMainCameraRef() || {
    current: null,
  };
  const thermalCameraRef = cameraViewRef.current?.getThermalCameraRef() || {
    current: null,
  };
  const activeView = cameraViewRef.current?.getActiveView() || "main";

  // Use the video aspect ratio hook to get dynamic sizing data
  const aspectRatioData = useVideoAspectRatio(
    mainCameraRef,
    thermalCameraRef,
    activeView
  );

  // Get CSS custom properties for dynamic layout
  const layoutStyles = getLayoutCSSProperties(aspectRatioData);

  return (
    <div
      className="h-screen bg-[--gray-9] flex flex-col overflow-hidden transition-all duration-300 ease-in-out"
      style={layoutStyles}
    >
      {/* Main layout container */}
      <div className="flex-1 flex overflow-hidden min-h-0">
        {/* Main content area - camera view */}
        <div className="flex-1 flex flex-col min-w-0 min-h-0">
          {/* Camera view container - takes remaining space after bottom panel */}
          <div className="flex-1 flex items-center justify-center p-4 min-h-0">
            <CameraView
              ref={cameraViewRef}
              className="transition-all duration-300 ease-in-out"
            />
          </div>

          {/* Bottom panel - dynamic height based on video aspect ratio */}
          <BottomPanel aspectRatioData={aspectRatioData} />
        </div>

        {/* Side panel - responsive width based on video aspect ratio */}
        <SidePanel aspectRatioData={aspectRatioData} />
      </div>
    </div>
  );
};

export default AntiMavicApp;
