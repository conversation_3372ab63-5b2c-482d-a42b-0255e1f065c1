import { useRef, useState } from "react";
import CameraView from "./components/CameraView";
import type { CameraViewRef } from "./components/CameraView";
import SidePanel from "./components/SidePanel";
import BottomPanel from "./components/BottomPanel";
import AspectRatioSelector from "./components/AspectRatioSelector";
import {
  useVideoAspectRatio,
  getLayoutCSSProperties,
} from "./utils/useVideoAspectRatio";

const AntiMavicApp = () => {
  const cameraViewRef = useRef<CameraViewRef>(null);
  const [manualAspectRatio, setManualAspectRatio] = useState<
    number | undefined
  >(undefined);

  // Get active view from CameraView component
  const activeView = cameraViewRef.current?.getActiveView() || "main";

  // Use the video aspect ratio hook to get dynamic sizing data
  const aspectRatioData = useVideoAspectRatio(activeView, manualAspectRatio);

  // Get CSS custom properties for dynamic layout
  const layoutStyles = getLayoutCSSProperties(aspectRatioData);

  return (
    <div
      className="h-screen bg-[--gray-9] flex flex-col overflow-hidden transition-all duration-300 ease-in-out"
      style={layoutStyles}
    >
      {/* Main layout container */}
      <div className="flex-1 flex overflow-hidden min-h-0">
        {/* Main content area - camera view */}
        <div className="flex-1 flex flex-col min-w-0 min-h-0">
          {/* Camera view container - takes remaining space after bottom panel */}
          <div className="flex-1 flex items-center justify-center p-4 min-h-0 relative">
            <CameraView
              ref={cameraViewRef}
              className="transition-all duration-300 ease-in-out"
            />

            {/* Aspect Ratio Selector - positioned in top-right of camera view */}
            <div className="absolute top-6 right-6 z-40">
              <AspectRatioSelector
                currentAspectRatio={aspectRatioData}
                onAspectRatioChange={setManualAspectRatio}
              />
            </div>
          </div>

          {/* Bottom panel - dynamic height based on video aspect ratio */}
          <BottomPanel aspectRatioData={aspectRatioData} />
        </div>

        {/* Side panel - responsive width based on video aspect ratio */}
        <SidePanel aspectRatioData={aspectRatioData} />
      </div>
    </div>
  );
};

export default AntiMavicApp;
