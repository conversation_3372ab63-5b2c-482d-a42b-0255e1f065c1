from fastapi import APIRouter, HTTPException, Response
from src.apps.auth.deps import AdminDep
from src.apps.deps import ObjID_Dep

from ..models import Modulation, ModulationDB

router = APIRouter()


router = APIRouter()


@router.get("/")
async def get_all_groups():
    return await ModulationDB.find().to_list()


@router.get("/{_id}")
async def get_one(_id: ObjID_Dep) -> ModulationDB | None:
    return await ModulationDB.find_one(_id)


@router.post("/", response_model=ModulationDB)
async def create_mod(new_mod: Modulation):
    mod = ModulationDB(**new_mod.model_dump())
    await mod.create()
    return mod


@router.patch("/{_id}")
async def update_mod(_id: ObjID_Dep, new_mod: dict):
    mod = await ModulationDB.find_one(_id)
    if not mod:
        raise HTTPException(404, "mod not found")

    mod = mod.model_copy(update=new_mod)
    await mod.save()


@router.delete("/")
async def delete_mod(_: AdminDep, _id: ObjID_Dep) -> Response:
    await ModulationDB.find_one(_id).delete()
    return Response(status_code=204)
