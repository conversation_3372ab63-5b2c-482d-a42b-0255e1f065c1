<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Highlight.js for syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <title>App State</title>
</head>
<body class="bg-gray-100 font-sans">
    <div class="container mx-auto p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">App State</h1>
        
        <!-- Search Input -->
        <div class="mb-4">
            <input 
                type="text" 
                id="search-input" 
                placeholder="Search by key..." 
                class="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                oninput="filterJson()"
            >
        </div>

        <!-- Buttons for WebSocket Control -->
        <div class="mb-4 flex space-x-4">
            <button 
                id="disconnect-btn" 
                onclick="disconnectWebSocket()" 
                class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
                Disconnect
            </button>
            <button 
                id="reconnect-btn" 
                onclick="reconnectWebSocket()" 
                class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
                Reconnect
            </button>
        </div>
        <div>
        <p id="label-label"></p>
        <div id="state-content" class="bg-white p-4 rounded-lg shadow-md">
            <pre><code class="language-json">Waiting for data...</code></pre>
        </div>
        </div>

    </div>

    <script>
        // Store the original JSON data and WebSocket globally
        let originalJsonData = null;
        let ws = null;

        // Function to initialize WebSocket
        function initWebSocket() {
            ws = new WebSocket("{{ ws_url }}");
            console.log("WebSocket initialized:", ws);

            ws.onmessage = function(event) {
                console.log("Received message:", event.data);
                let label = document.getElementById('label-label');
                label.innerHTML = `<pre class="text-green-500">WebSocket connected</pre>`;
                try {
                    originalJsonData = JSON.parse(event.data);
                    displayFilteredJson(originalJsonData);
                } catch (e) {
                    let state = document.getElementById('state-content');
                    state.innerHTML = `<pre class="text-red-600">Error: Invalid JSON - ${event.data}</pre>`;
                }
            };
            ws.onclose = function() {
                console.log("WebSocket connection closed");
                let label = document.getElementById('label-label');
                label.innerHTML = `<pre class="text-red-500">WebSocket closed</pre>`;
            };

            ws.onerror = function(error) {
                console.log("WebSocket error:", error);
                let label = document.getElementById('label-label');
                label.innerHTML = `<pre class="text-red-600">WebSocket error occurred</pre>`;
            };
        }

        // Function to disconnect WebSocket
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                console.log("WebSocket manually closed");
            }
        }

        // Function to reconnect WebSocket
        function reconnectWebSocket() {
            if (!ws || ws.readyState === WebSocket.CLOSED) {
                initWebSocket();
            } else {
                console.log("WebSocket is already connected");
            }
        }

        // Function to filter JSON based on search input
        function filterJson() {
            if (!originalJsonData) return;

            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            let filteredData;

            if (searchTerm === "") {
                filteredData = originalJsonData;
            } else {
                filteredData = {};
                Object.keys(originalJsonData).forEach(key => {
                    if (key.toLowerCase().includes(searchTerm)) {
                        filteredData[key] = originalJsonData[key];
                    }
                });
            }

            displayFilteredJson(filteredData);
        }

        // Function to display JSON with highlighting
        function displayFilteredJson(data) {
            const formattedJson = JSON.stringify(data, null, 2);
            let state = document.getElementById('state-content');
            state.innerHTML = `<pre><code class="language-json">${formattedJson}</code></pre>`;
            hljs.highlightAll();
        }

        // Initial setup
        document.addEventListener('DOMContentLoaded', (event) => {
            hljs.highlightAll();
            initWebSocket(); // Start WebSocket on page load
        });
    </script>
</body>
</html>