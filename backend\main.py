if __name__ == "__main__":
    import uvicorn
    from loguru import logger
    from src.conf.settings import settings

    logger.info(f"Environment type: {settings.env_type}")

    match settings.env_type:
        case "dev":
            uvicorn.run(
                # "src.app.main:app",
                "src.apps.main:app",
                host="0.0.0.0",
                port=settings.server_port,
                reload=True,
            )
        case "prod":
            uvicorn.run(
                "src.apps.main:app",
                host="0.0.0.0",
                port=settings.server_port,
                workers=1,
            )
        case _:
            logger.error(f"Invalid environment type: {settings.env_type}")
            exit()
