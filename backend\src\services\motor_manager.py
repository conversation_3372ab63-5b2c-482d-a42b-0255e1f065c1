from bson import ObjectId
from fastapi import HTTPException
from motor.motor_asyncio import AsyncIOMotorClient
from src.apps.utils import serialize_mongo_document
from src.conf import settings


class MotorManager(AsyncIOMotorClient):
    @staticmethod
    def make_obj_from_id(_id: str | ObjectId | dict) -> dict[str, ObjectId]:
        match _id:
            case str():
                return {"_id": ObjectId(_id)}
            case ObjectId():
                return {"_id": _id}
        return _id

    @classmethod
    async def get_by_id_or_err(cls, collection, _id: dict | str | ObjectId) -> dict:
        if res := await cls.get_by_id(collection, _id):
            return res
        raise HTTPException(status_code=404, detail="not found")

    @classmethod
    async def get_by_id(cls, collection, _id: dict | str | ObjectId) -> dict | None:
        _id = cls.make_obj_from_id(_id)
        resp = await collection.find_one(_id)
        if resp:
            return cls.serialize(resp)

    @staticmethod
    def serialize_one(document) -> dict:
        """
        Serialize a MongoDB document, converting ObjectId fields to strings.

        Args:
            document: A dictionary or other data structure from MongoDB.

        Returns:
            A JSON-serializable copy of the document with ObjectId fields as strings.
        """
        if not isinstance(document, dict):
            return document

        serialized = {}
        for key, value in document.items():
            if isinstance(value, ObjectId):
                serialized[key] = str(value)
            elif isinstance(value, dict):
                serialized[key] = serialize_mongo_document(value)
            elif isinstance(value, list):
                serialized[key] = [
                    serialize_mongo_document(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                serialized[key] = value

        return serialized

    @classmethod
    def serialize(cls, document) -> dict | list:
        if isinstance(document, list):
            return [cls.serialize_one(item) for item in document]
        return cls.serialize_one(document)
