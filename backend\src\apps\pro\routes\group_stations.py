from beanie.operators import AddToSet, Pull
from fastapi import APIRouter, HTTPException, Response
from src.apps.auth.deps import AdminDep
from src.apps.deps import ObjID_Dep
from src.apps.pro.schemas import Add_Or_Remove_Relation

from ..models import StationDB, StationGroup, StationGroupDB

router = APIRouter()


router = APIRouter()


@router.get("/")
async def get_all_groups():
    return await StationGroupDB.find().to_list()


@router.get("/{_id}")
async def get_one(_id: ObjID_Dep) -> StationGroupDB | None:
    return await StationGroupDB.find_one(_id, fetch_links=True)


@router.post("/", response_model=StationGroupDB)
async def create_group(new_group: StationGroup):
    print(new_group, "new_group")
    group = StationGroupDB(**new_group.model_dump())
    await group.create()
    return group


@router.patch("/{_id}")
async def update_group(d: Add_Or_Remove_Relation):
    group = await StationGroupDB.get(d.subject_id)
    station = await StationDB.get(d.object_id)
    if not group or not station:
        raise HTTPException(404, "Not found")
    q = {StationGroupDB.stations: d.object_id}
    if d.action == "add":
        q = AddToSet(q)
    else:
        q = Pull(q)
    await group.update(q)
    await group.save()


@router.delete("/")
async def delete_group(_: AdminDep, _id: ObjID_Dep) -> Response:
    await StationGroupDB.find_one(_id).delete()
    return Response(status_code=204)
