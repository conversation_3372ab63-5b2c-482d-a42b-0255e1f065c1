import axiosClient from "../../../lib/axiosClient";

export interface Target {
  _id?: string;
  lat: number;
  lng: number;
  name?: string;
  type?: string;
  height?: number;
  videoFrequency?: string;
  telemetryFrequency?: string;
  // Add other fields as needed
}

export const targetService = {
  // Get all targets
  async getAllTargets(): Promise<Target[]> {
    const response = await axiosClient.get<Target[]>("/map/targets/");
    return response.data || [];
  },

  // Create a new target
  async createTarget(target: Omit<Target, "_id">): Promise<Target | null> {
    const response = await axiosClient.post<Target>("/map/targets/", target);
    return response.data || null;
  },

  // Get target by ID
  async getTargetById(_id: string): Promise<Target | null> {
    const response = await axiosClient.get<Target>(`/map/targets/${_id}`);
    return response.data || null;
  },

  // Update target
  async updateTarget(
    _id: string,
    target: Partial<Target>
  ): Promise<Target | null> {
    // Create a clean version of the target data without _id
    const { _id: targetId, ...cleanTargetData } = target;

    const response = await axiosClient.put<Target>(
      `/map/targets/${_id}`,
      cleanTargetData
    );

    // If we don't get back a full target object from the API, create a merged one
    if (response.data) {
      // Make sure all fields from the update are in the returned data
      return {
        ...(response.data || {}),
        ...cleanTargetData,
        _id: _id,
      };
    }

    return null;
  },

  // Delete target
  async deleteTarget(_id: string): Promise<boolean> {
    try {
      console.log(`Attempting to delete target with ID: ${_id}`);
      const response = await axiosClient.delete(`/map/targets/${_id}`);
      const success = response.status === 200 || response.status === 204;
      console.log(`Delete target response:`, response);
      return success;
    } catch (error) {
      console.error(`Error deleting target with ID ${_id}:`, error);
      return false;
    }
  },
};
