from fastapi import APIRouter, HTTPException, Response
from src.apps.auth.deps import AdminDep, UserDep
from src.apps.deps import ObjID_Dep

from ..models.amplifier import Amplifier, AmplifierDB

router = APIRouter()

router = APIRouter()


@router.get("/")
async def get_all_amplifiers():
    return await AmplifierDB.find().to_list()


@router.get("/{_id}")
async def get_one(_id: ObjID_Dep):
    return await AmplifierDB.find_one(_id)


@router.post("/")
async def create_amplifier(
    user: UserDep, new_amplifier: Amplifier
) -> AmplifierDB | None:
    if not new_amplifier.org:
        new_amplifier.org = user.org
    amplifier = AmplifierDB(**new_amplifier.model_dump())
    await amplifier.create()
    return amplifier


@router.patch("/{_id}")
async def update_amplifier(_id: ObjID_Dep, new_amplifier: dict):
    amplifier = await AmplifierDB.find_one(_id)
    if not amplifier:
        raise HTTPException(404, "amplifier not found")

    amplifier = amplifier.model_copy(update=new_amplifier)
    await amplifier.save()


@router.delete("/")
async def delete_amplifier(_: AdminDep, _id: ObjID_Dep) -> Response:
    await AmplifierDB.find_one(_id).delete()
    return Response(status_code=204)
