import { useState, useEffect, useCallback } from "react";
import { CAMERA_SPECS } from "./config";

export interface VideoContainerSize {
  width: number;
  height: number;
  scale: number;
  aspectRatio: number;
  containerStyle: React.CSSProperties;
}

export const useVideoContainerSize = (
  activeView: "main" | "thermal",
  containerRef: React.RefObject<HTMLDivElement | null>
): VideoContainerSize => {
  const [containerSize, setContainerSize] = useState<VideoContainerSize>({
    width: 1920,
    height: 1080,
    scale: 1,
    aspectRatio: 16 / 9,
    containerStyle: {},
  });

  const calculateOptimalSize = useCallback(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();

    // Get available space (subtract padding)
    const availableWidth = containerRect.width - 32; // 16px padding on each side
    const availableHeight = containerRect.height - 32; // 16px padding on each side

    if (availableWidth <= 0 || availableHeight <= 0) return;

    // Get camera specs for current view
    const cameraSpec = CAMERA_SPECS[activeView];
    const videoAspectRatio = cameraSpec.aspectRatio;

    // Calculate container aspect ratio
    const containerAspectRatio = availableWidth / availableHeight;

    let optimalWidth: number;
    let optimalHeight: number;
    let scale: number;

    if (containerAspectRatio > videoAspectRatio) {
      // Container is wider than video - fit to height and crop width
      optimalHeight = availableHeight;
      optimalWidth = optimalHeight * videoAspectRatio;
      scale = optimalHeight / cameraSpec.height;
    } else {
      // Container is taller than video - fit to width and crop height
      optimalWidth = availableWidth;
      optimalHeight = optimalWidth / videoAspectRatio;
      scale = optimalWidth / cameraSpec.width;
    }

    // Create container style for perfect fit without black borders
    const containerStyle: React.CSSProperties = {
      width: `${optimalWidth}px`,
      height: `${optimalHeight}px`,
      aspectRatio: videoAspectRatio.toString(),
      transition: "width 300ms ease, height 300ms ease",
    };

    const newSize: VideoContainerSize = {
      width: optimalWidth,
      height: optimalHeight,
      scale,
      aspectRatio: videoAspectRatio,
      containerStyle,
    };

    setContainerSize(newSize);

    console.log(`📐 Video container optimized for ${activeView}:`, {
      camera: `${cameraSpec.width}×${cameraSpec.height}`,
      aspectRatio: videoAspectRatio.toFixed(3),
      container: `${optimalWidth.toFixed(0)}×${optimalHeight.toFixed(0)}`,
      scale: scale.toFixed(3),
      available: `${availableWidth}×${availableHeight}`,
    });
  }, [activeView, containerRef]);

  // Debounced resize handler to avoid excessive calculations
  const debouncedCalculate = useCallback(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(calculateOptimalSize, 100);
    };
  }, [calculateOptimalSize]);

  useEffect(() => {
    // Initial calculation
    calculateOptimalSize();

    // Set up ResizeObserver for the container
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      debouncedCalculate()();
    });

    resizeObserver.observe(containerRef.current);

    // Listen for window resize as backup
    const handleWindowResize = debouncedCalculate();
    window.addEventListener("resize", handleWindowResize);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("resize", handleWindowResize);
    };
  }, [calculateOptimalSize, debouncedCalculate, containerRef]);

  // Recalculate when active view changes
  useEffect(() => {
    const timer = setTimeout(calculateOptimalSize, 100);
    return () => clearTimeout(timer);
  }, [activeView, calculateOptimalSize]);

  return containerSize;
};

// Utility function to get iframe styles for perfect video fit
export const getIframeStyles = (
  containerSize: VideoContainerSize,
  isActive: boolean,
  isLoading: boolean
): React.CSSProperties => {
  return {
    width: "100%",
    height: "100%",
    border: "none",
    objectFit: "cover", // Use cover to fill container completely
    objectPosition: "center",
    opacity: isLoading ? 0 : isActive ? 1 : 0,
    transition: "opacity 0.3s ease",
  };
};
