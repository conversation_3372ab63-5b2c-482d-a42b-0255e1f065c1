"""User models."""

from typing import Annotated, Any, Literal, Optional

from beanie import Document, Indexed, Link, PydanticObjectId
from pydantic import BaseModel

from . import OrgDB

UserRole = Literal["user", "organizer", "creator", "developer"]

"""
developer - має доступ до абсолютно всіх CRUD операцій та доступ до всіх полів
creator - має доступ тільки на читання та створення станцій
organizer - доступ тільки до юнітів чи станцій які надав йому dev чи creator, можливість створення юзерів та надання прав доступу їй до можливих станцій чи юнітів, також додавання присетів
user - тільки керування та додавання присетів
"""


class User(BaseModel):
    """User register and login auth."""

    username: str
    password: str
    role: UserRole = "user"
    org: PydanticObjectId | None = None


class UserDB(Document, User):
    username: Annotated[str, Indexed(str, unique=True)]
    org: Link[OrgDB]

    class Settings:
        name = "users"

    # @property
    # def jwt_subject(self) -> dict[str, Any]:
    #     """JWT subject fields."""
    #     return {"username": self.username}

    @classmethod
    async def by_username(cls, username: str) -> Optional["UserDB"]:
        """Get a user by username."""
        return await cls.find_one(UserDB.username == username)

    @property
    def jwt_subject(self) -> dict[str, Any]:
        """JWT subject fields."""
        return {"username": self.username, "role": self.role}
