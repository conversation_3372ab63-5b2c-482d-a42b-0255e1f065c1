import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  APIProvider,
  Map as GoogleM<PERSON>,
  Marker,
} from "@vis.gl/react-google-maps";

interface MapOverlayProps {
  onMapClick: () => void;
  isMainView?: boolean;
}

const MapOverlay: React.FC<MapOverlayProps> = ({
  onMapClick,
  isMainView = false,
}) => {
  const { t } = useTranslation("antimavic");
  const [isHovered, setIsHovered] = useState(false);

  // Hardcoded camera position for now
  const position = { lat: 50.45, lng: 30.52 };

  const handleClick = () => {
    onMapClick();
  };

  if (isMainView) {
    // Full size map view
    return (
      <div
        className="w-full h-full rounded-lg overflow-hidden relative group cursor-pointer"
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <APIProvider apiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}>
          <GoogleMap
            defaultCenter={position}
            defaultZoom={15}
            gestureHandling="greedy"
            mapId="8e0a97af9386fef"
            disableDefaultUI={true}
            mapTypeId={"satellite"}
          >
            <Marker position={position} />
          </GoogleMap>
        </APIProvider>

        {/* Hover tooltip for opening video */}
        {isHovered && (
          <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1.5 rounded-md text-sm z-10 pointer-events-none">
            {t("map.openVideo", "Open Video")}
          </div>
        )}
      </div>
    );
  }

  // Mini map overlay (bottom-left of video)
  return (
    <div
      className="absolute bottom-4 left-4 w-56 h-36 rounded-lg overflow-hidden cursor-pointer group z-30 shadow-xl border border-white/20"
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        background: "rgba(0, 0, 0, 0.2)",
        backdropFilter: "blur(4px)",
      }}
    >
      <div className="w-full h-full relative">
        <APIProvider apiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}>
          <GoogleMap
            defaultCenter={position}
            defaultZoom={13}
            gestureHandling="none"
            mapId="8e0a97af9386fef"
            disableDefaultUI={true}
            mapTypeId={"satellite"}
            zoomControl={false}
            mapTypeControl={false}
            scaleControl={false}
            streetViewControl={false}
            rotateControl={false}
            fullscreenControl={false}
            clickableIcons={false}
          >
            <Marker position={position} />
          </GoogleMap>
        </APIProvider>

        {/* Hover tooltip */}
        {isHovered && (
          <div className="absolute bottom-2 left-2 bg-black/80 text-white px-2 py-1 rounded text-xs z-10 pointer-events-none whitespace-nowrap">
            {t("map.openOnMap", "Open on map")}
          </div>
        )}

        {/* Subtle overlay for better contrast */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent group-hover:from-black/10 transition-all duration-200 pointer-events-none" />
      </div>
    </div>
  );
};

export default MapOverlay;
