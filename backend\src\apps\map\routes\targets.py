from fastapi import APIRouter
from src.apps.deps import ObjID_Dep
from src.conf.loader import get_motor_manager

router = APIRouter()

db = get_motor_manager()

collection = db.map.targets


# Create a new target
@router.post("/", status_code=201)
async def create_target(data: dict):
    result = await collection.insert_one(data)
    return await db.get_by_id(collection, result.inserted_id)


# Read a target by ID
@router.get("/{_id}")
async def read_target(_id: ObjID_Dep):
    return await db.get_by_id_or_err(collection, _id)


# Read all target
@router.get("/")
async def read_all_target():
    targets = await collection.find().to_list()
    return db.serialize(targets)


# Update a target
@router.put("/{_id}")
async def update_target(_id: ObjID_Dep, target: dict):
    await collection.update_one(_id, {"$set": target})


# Delete a target
@router.delete("/{_id}", status_code=204)
async def delete_target(_id: ObjID_Dep):
    await collection.delete_one(_id)
