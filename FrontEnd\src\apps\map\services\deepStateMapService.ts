export interface DeepStateMapFeatureProperties {
  fillColor?: string | null;
  fillOpacity?: number | null;
  strokeColor?: string | null;
  strokeOpacity?: number | null;
  strokeWeight?: number | null;
}

export interface DeepStateMapFeature {
  coordinates?: google.maps.LatLngLiteral[];
  properties?: DeepStateMapFeatureProperties;
}

export interface DeepStateMap {
  id?: number | null;
  datetime?: string | null;
  features?: DeepStateMapFeature[];
}

const DEEPSTATE_API_URL = import.meta.env.VITE_DEEPSTATE_API_URL;

const parseDeepStateLastFeatures = (value: unknown): DeepStateMap => {
  const data = value as any;
  
  const id = data?.id || null;
  const datetime = data?.datetime || '';

  const features = data?.map?.features?.reduce((result: DeepStateMapFeature[], item: any) => {
    if (
      item?.type !== 'Feature' ||
      item?.geometry?.type !== 'Polygon' ||
      item?.properties?.name?.indexOf('Liberated') !== -1
    ) {
      return result;
    }

    const coordinates: google.maps.LatLngLiteral[] = item?.geometry?.coordinates?.[0]?.map(
      (cItem: number[]) => ({
        lat: cItem[1],
        lng: cItem[0],
      })
    ) || [];

    const properties: DeepStateMapFeatureProperties = {
      fillColor: item?.properties?.fill || '#ff0000',
      fillOpacity: item?.properties?.['fill-opacity'] || 0.3,
      strokeColor: item?.properties?.stroke || '#ff0000',
      strokeWeight: 2,
      strokeOpacity: item?.properties?.['stroke-opacity'] || 0.8,
    };

    return [
      ...result,
      {
        coordinates,
        properties,
      },
    ];
  }, []) || [];

  return {
    id,
    datetime,
    features,
  };
};

export const fetchDeepStateMapLastFeatures = async (): Promise<DeepStateMap> => {
  try {
    const response = await fetch(DEEPSTATE_API_URL);

    if (!response.ok) {
      throw new Error(`Can't fetch data from DeepStateMap API! Status: ${response.status}`);
    }

    const json = await response.json();
    return parseDeepStateLastFeatures(json);
  } catch (error) {
    console.error('Error fetching DeepState map data:', error);
    throw error;
  }
}; 