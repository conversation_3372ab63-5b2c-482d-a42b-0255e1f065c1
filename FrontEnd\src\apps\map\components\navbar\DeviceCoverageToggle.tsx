import { useTranslation } from "react-i18next";

interface DeviceCoverageToggleProps {
  isVisible: boolean;
  onToggle: (visible: boolean) => void;
}

export const DeviceCoverageToggle = ({
  isVisible,
  onToggle,
}: DeviceCoverageToggleProps) => {
  const { t } = useTranslation("map");

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-700 dark:text-[--sand-light]">
          {t("device_coverage")}
        </span>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={isVisible}
            onChange={(e) => onToggle(e.target.checked)}
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-[--military-light] peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-[--military-accent] peer-checked:bg-[--green-7] dark:peer-checked:bg-[--green-5]"></div>
        </label>
      </div>
      <p className="text-xs text-gray-500 dark:text-[--sand-light]">
        {t("show_device_coverage_description")}
      </p>
    </div>
  );
}; 