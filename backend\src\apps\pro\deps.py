from typing import Annotated

from fastapi import Depends, HTTPException, Request
from src.apps.pro.controller import Controller
from src.conf.loader import get_motor_manager


db = get_motor_manager()


# Generic function to get objects from app.state
def get_obj_from_app_state(name: str):
    def _get_obj(request: Request):
        return getattr(request.app.state, name)

    return _get_obj


async def get_device(device_id: str):
    if device_db := await db.get_by_id(db.pro.devices, device_id):
        return device_db
    raise HTTPException(404, "Device not found")


DeviceDeps = Annotated[dict, Depends(get_device)]
ControllerDeps = Annotated[Controller, Depends(get_obj_from_app_state("controller"))]
