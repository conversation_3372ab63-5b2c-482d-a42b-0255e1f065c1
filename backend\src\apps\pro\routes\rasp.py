import io
import json

import boto3
from fastapi import APIRouter, HTTPException, Request, UploadFile
from src.apps.deps import MQTTDeps

router = APIRouter()

s3_client = boto3.client("s3")


@router.post("/upload_logs/{rasp_id}")
async def upload_logs_to_s3(file: UploadFile, rasp_id: str):

    # Define your bucket name and file key (path in S3)
    bucket_name = "rasps-logs"

    # Read the file contents
    contents = await file.read()
    with io.BytesIO() as temp_file:
        temp_file.write(contents)
        temp_file.seek(0)
        key = f"{rasp_id}/{file.filename}"
        # Upload to S3
        s3_client.put_object(
            Bucket=bucket_name, Key=key, Body=contents, ContentType=file.content_type
        )

    # Optionally return the URL or a success message
    file_url = f"https://{bucket_name}.s3.amazonaws.com/{key}"
    return {"filename": file.filename, "url": file_url}


@router.post("/execute_cmd")
async def execute_cmd(mqtt: MQTTDeps, rasp_id: str, cmd: str):
    return await mqtt.publish_with_resp(
        "execute_cmd", {"cmd": cmd}, raspberry_id=rasp_id
    )


@router.post("/get_rasp_password")
async def get_rasp_password(request: Request):
    secret_bucket = "asap-secret"
    rasp_passwords_file_name = "raspberry_passwords.json"

    def get_passwords_data():
        try:
            response = s3_client.get_object(
                Bucket=secret_bucket, Key=rasp_passwords_file_name
            )
            return json.loads(response["Body"].read().decode("utf-8"))
        except Exception as e:
            raise HTTPException(f"Error: {e}")

    passwords = get_passwords_data()
    if password := passwords.get(request.client.host):
        return password
    raise HTTPException(404, f"Password not found for ip: {request.client.host}")
