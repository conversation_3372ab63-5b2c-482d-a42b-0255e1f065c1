import { useTranslation } from "react-i18next";
import Map from "./controls/Map";
import Stats from "./controls/Stats";

const CameraControls = () => {
  const { t } = useTranslation("antimavic");

  return (
    <div className="bg-[--gray-9] p-2 rounded-lg text-[--gray-1] w-full max-h-[30vh] flex gap-2">
      {/* Column 1 - Map */}
      <div className="flex-[2_2_0%] h-full">
        <Map />
      </div>

      {/* Column 2 - Stats */}
      <div className="flex-[2_2_0%] h-full py-1">
        <Stats />
      </div>
    </div>
  );
};

export default CameraControls;
