from fastapi import APIRouter
from src.apps.deps import ObjID_Dep
from src.conf.loader import get_motor_manager

router = APIRouter()

db = get_motor_manager()

collection = db.map.units


# Create a new device
@router.post("/", status_code=201)
async def create_device(data: dict):
    result = await collection.insert_one(data)
    return await db.get_by_id(collection, result.inserted_id)


# Read a device by ID
@router.get("/{_id}")
async def read_device(_id: ObjID_Dep):
    return await db.get_by_id_or_err(collection, _id)


# Read all device
@router.get("/")
async def read_all_device():
    units = await collection.find().to_list()
    return db.serialize(units)


# Update a device
@router.put("/{_id}")
async def update_device(_id: ObjID_Dep, device: dict):
    await collection.update_one(_id, {"$set": device})


# Delete a device
@router.delete("/{_id}", status_code=204)
async def delete_device(_id: ObjID_Dep):
    await collection.delete_one(_id)
