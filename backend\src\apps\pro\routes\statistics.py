from datetime import datetime, timezone

import polars as pl
from fastapi import APIRouter
from src.apps.deps import TSDBDeps
from src.conf.settings import settings

router = APIRouter()


@router.get("/device_activity/")
async def device_activity(
    ts_db: TSDBDeps,
    raspberry_id: str = None,
    start_time: str = None,  # UTC timestamp, e.g., "2025-04-07T15:00:00Z"
    end_time: str = None,  # UTC timestamp, e.g., "2025-04-07T16:00:00Z"
):
    # Handle end_time (convert to UTC datetime if provided, else use current UTC time)
    if end_time:
        end_time = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
    else:
        end_time = datetime.now(timezone.utc)
    end_time = end_time.astimezone(settings.current_tz)

    # Handle start_time (convert to UTC datetime if provided)
    if start_time:
        start_time = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
        start_time = start_time.astimezone(settings.current_tz)

    # Base SQL statement
    stm = "SELECT * FROM device_activity"

    # Build WHERE clause
    conditions = []

    # Add raspberry_id filter if provided
    if raspberry_id:
        conditions.append(f"raspberry_id = '{raspberry_id}'")

    # Add start_time filter if provided
    if start_time:
        conditions.append(f"time >= '{start_time}'")

    # Combine conditions
    stm_conditions = "AND ".join(conditions)
    stm += f" WHERE time <= '{end_time}'"
    if stm_conditions:
        stm += f" AND {stm_conditions}"
    print(stm, "stmawdawd")
    # Execute the query
    result: pl.DataFrame = ts_db.query(stm)

    # Return as list of dicts for JSON response
    return result.to_dicts()
