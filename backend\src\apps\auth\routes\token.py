from typing import Annotated

from fastapi import APIRouter, Form
from pydantic import BaseModel

from ..exeptions import credentials_exception
from ..jwt import access_security
from ..models.user import UserDB, UserRole
from ..utils.password import verify_password

router = APIRouter()


class Token(BaseModel):
    access_token: str
    token_type: str
    role: UserRole


@router.post("/")
async def login(
    username: Annotated[str, Form()],
    password: Annotated[str, Form()],
) -> Token:

    user = await UserDB.by_username(username)
    if user is None or not verify_password(password, user.password):
        raise credentials_exception
    access_token = access_security.create_access_token(user.jwt_subject)
    return Token(access_token=access_token, token_type="bearer", role=user.role)
