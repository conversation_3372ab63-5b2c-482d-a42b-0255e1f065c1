from fastapi import APIRouter, HTTPException, Response
from src.apps.auth.deps import AdminDep
from src.apps.deps import ObjID_Dep

from ..models import Unit, UnitDB

router = APIRouter()


router = APIRouter()


@router.get("/")
async def get_all_groups():
    return await UnitDB.find().to_list()


@router.get("/{_id}")
async def get_one(_id: ObjID_Dep) -> UnitDB | None:
    return await UnitDB.find_one(_id, fetch_links=True)


@router.post("/", response_model=UnitDB)
async def create_group(new_group: Unit):
    group = UnitDB(**new_group.model_dump())
    await group.create()
    return group


@router.patch("/{_id}")
async def update_group(_id: ObjID_Dep, new_group: dict):
    group = await UnitDB.find_one(_id)
    if not group:
        raise HTTPException(404, "group not found")

    group = group.model_copy(update=new_group)
    await group.save()


@router.delete("/")
async def delete_group(_: AdminDep, _id: ObjID_Dep) -> Response:
    await UnitDB.find_one(_id).delete()
    return Response(status_code=204)
