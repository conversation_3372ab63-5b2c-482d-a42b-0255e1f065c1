import { useState } from "react";
import { Monitor, Square, Maximize2, Camera } from "lucide-react";
import type { AspectRatioData } from "../utils/useVideoAspectRatio";
import { CAMERA_SPECS } from "../utils/config";

interface AspectRatioSelectorProps {
  currentAspectRatio: AspectRatioData;
  onAspectRatioChange: (aspectRatio: number) => void;
  className?: string;
}

const AspectRatioSelector = ({
  currentAspectRatio,
  onAspectRatioChange,
  className = "",
}: AspectRatioSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const aspectRatioOptions = [
    {
      ratio: CAMERA_SPECS.main.aspectRatio,
      label: "Main Cam",
      description: "2688×1520",
      icon: Camera,
    },
    {
      ratio: CAMERA_SPECS.thermal.aspectRatio,
      label: "Thermal",
      description: "1280×720",
      icon: Camera,
    },
    {
      ratio: 21 / 9,
      label: "21:9",
      description: "Ultra-wide",
      icon: Maximize2,
    },
    { ratio: 16 / 9, label: "16:9", description: "Widescreen", icon: Monitor },
    { ratio: 4 / 3, label: "4:3", description: "Standard", icon: Square },
    { ratio: 1, label: "1:1", description: "Square", icon: Square },
    { ratio: 9 / 16, label: "9:16", description: "Portrait", icon: Monitor },
  ];

  const handleAspectRatioSelect = (ratio: number) => {
    onAspectRatioChange(ratio);
    setIsOpen(false);
  };

  const getCurrentOption = () => {
    return (
      aspectRatioOptions.find(
        (option) =>
          Math.abs(option.ratio - currentAspectRatio.aspectRatio) < 0.1
      ) || aspectRatioOptions[1]
    ); // Default to 16:9
  };

  const currentOption = getCurrentOption();
  const IconComponent = currentOption.icon;

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-1.5 bg-black/40 hover:bg-black/60 text-white rounded-md text-sm transition-colors"
        aria-label="Select video aspect ratio"
      >
        <IconComponent size={16} />
        <span>{currentOption.label}</span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? "rotate-180" : ""}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-1 bg-black/90 backdrop-blur-sm rounded-lg shadow-lg border border-gray-700 z-50 min-w-[200px]">
            <div className="p-2">
              <div className="text-xs text-gray-400 px-2 py-1 mb-1">
                Video Aspect Ratio
              </div>
              {aspectRatioOptions.map((option) => {
                const OptionIcon = option.icon;
                const isSelected =
                  Math.abs(option.ratio - currentAspectRatio.aspectRatio) < 0.1;

                return (
                  <button
                    key={option.label}
                    onClick={() => handleAspectRatioSelect(option.ratio)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors ${
                      isSelected
                        ? "bg-green-600/20 text-green-400"
                        : "hover:bg-gray-700/50 text-gray-300"
                    }`}
                  >
                    <OptionIcon size={16} />
                    <div className="flex-1 text-left">
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-gray-500">
                        {option.description}
                      </div>
                    </div>
                    {isSelected && (
                      <div className="w-2 h-2 bg-green-400 rounded-full" />
                    )}
                  </button>
                );
              })}
            </div>

            <div className="border-t border-gray-700 p-2">
              <div className="text-xs text-gray-500 px-2">
                Current: {currentAspectRatio.category} (
                {currentAspectRatio.aspectRatio.toFixed(2)})
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default AspectRatioSelector;
