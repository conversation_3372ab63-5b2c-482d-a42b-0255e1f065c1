import { useState, useEffect, useCallback } from "react";
import type { RefObject } from "react";
import { LAYOUT_CONFIG } from "./config";

export interface AspectRatioData {
  aspectRatio: number;
  category: "ultraWide" | "wide" | "standard" | "square" | "portrait";
  sidePanelWidth: number;
  bottomPanelHeight: number;
  videoWidth: number;
  videoHeight: number;
}

export const useVideoAspectRatio = (
  mainCameraRef: RefObject<HTMLIFrameElement | null>,
  thermalCameraRef: RefObject<HTMLIFrameElement | null>,
  activeView: "main" | "thermal"
): AspectRatioData => {
  const [aspectRatioData, setAspectRatioData] = useState<AspectRatioData>(
    () => {
      // Default to wide aspect ratio (16:9)
      const defaultAspectRatio = 16 / 9;
      return calculateAspectRatioData(defaultAspectRatio, 1920, 1080);
    }
  );

  const calculateAspectRatioData = useCallback(
    (aspectRatio: number, width: number, height: number): AspectRatioData => {
      const {
        aspectRatioBreakpoints,
        panelAdjustments,
        defaultSidePanelWidth,
        defaultBottomPanelHeight,
        constraints,
      } = LAYOUT_CONFIG;

      // Determine aspect ratio category
      let category: AspectRatioData["category"];
      if (aspectRatio > aspectRatioBreakpoints.ultraWide) {
        category = "ultraWide";
      } else if (aspectRatio > aspectRatioBreakpoints.wide) {
        category = "wide";
      } else if (aspectRatio > aspectRatioBreakpoints.standard) {
        category = "standard";
      } else if (aspectRatio > aspectRatioBreakpoints.square) {
        category = "square";
      } else {
        category = "portrait";
      }

      // Calculate panel sizes based on category
      const adjustments = panelAdjustments[category];
      const calculatedSidePanelWidth = Math.round(
        defaultSidePanelWidth * adjustments.sidePanelWidthMultiplier
      );
      const calculatedBottomPanelHeight = Math.round(
        defaultBottomPanelHeight * adjustments.bottomPanelHeightMultiplier
      );

      // Apply constraints
      const sidePanelWidth = Math.max(
        constraints.sidePanelMinWidth,
        Math.min(constraints.sidePanelMaxWidth, calculatedSidePanelWidth)
      );

      const bottomPanelHeight = Math.max(
        constraints.bottomPanelMinHeight,
        Math.min(constraints.bottomPanelMaxHeight, calculatedBottomPanelHeight)
      );

      return {
        aspectRatio,
        category,
        sidePanelWidth,
        bottomPanelHeight,
        videoWidth: width,
        videoHeight: height,
      };
    },
    []
  );

  const detectVideoAspectRatio = useCallback(() => {
    const currentRef = activeView === "main" ? mainCameraRef : thermalCameraRef;

    if (!currentRef.current) return;

    try {
      // For iframe-based video streams, we need to try to get dimensions from the iframe content
      // Since we can't access iframe content due to CORS, we'll use a different approach

      // Method 1: Try to get dimensions from iframe element itself
      const iframe = currentRef.current;
      const iframeRect = iframe.getBoundingClientRect();

      if (iframeRect.width > 0 && iframeRect.height > 0) {
        const aspectRatio = iframeRect.width / iframeRect.height;
        const newData = calculateAspectRatioData(
          aspectRatio,
          iframeRect.width,
          iframeRect.height
        );
        // Only update if the aspect ratio has changed significantly (more than 0.1 difference)
        if (Math.abs(aspectRatioData.aspectRatio - aspectRatio) > 0.1) {
          setAspectRatioData(newData);
          console.log(
            `📐 Video aspect ratio updated: ${aspectRatio.toFixed(2)} (${newData.category})`
          );
        }
        return;
      }

      // Method 2: Use ResizeObserver to monitor iframe size changes
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          if (width > 0 && height > 0) {
            const aspectRatio = width / height;
            const newData = calculateAspectRatioData(
              aspectRatio,
              width,
              height
            );
            // Only update if the aspect ratio has changed significantly
            if (Math.abs(aspectRatioData.aspectRatio - aspectRatio) > 0.1) {
              setAspectRatioData(newData);
              console.log(
                `📐 Video aspect ratio updated via ResizeObserver: ${aspectRatio.toFixed(2)} (${newData.category})`
              );
            }
          }
        }
      });

      resizeObserver.observe(iframe);

      // Cleanup function
      return () => {
        resizeObserver.disconnect();
      };
    } catch (error) {
      console.warn("Could not detect video aspect ratio:", error);
    }
  }, [activeView, mainCameraRef, thermalCameraRef, calculateAspectRatioData]);

  // Alternative method: Listen for window resize and recalculate
  const handleWindowResize = useCallback(() => {
    detectVideoAspectRatio();
  }, [detectVideoAspectRatio]);

  useEffect(() => {
    // Initial detection
    const cleanup = detectVideoAspectRatio();

    // Listen for window resize events
    window.addEventListener("resize", handleWindowResize);

    // Set up interval to periodically check for changes
    const interval = setInterval(() => {
      detectVideoAspectRatio();
    }, 2000); // Check every 2 seconds

    return () => {
      if (cleanup) cleanup();
      window.removeEventListener("resize", handleWindowResize);
      clearInterval(interval);
    };
  }, [detectVideoAspectRatio, handleWindowResize]);

  // Re-detect when active view changes
  useEffect(() => {
    const timer = setTimeout(() => {
      detectVideoAspectRatio();
    }, 500); // Small delay to allow iframe to load

    return () => clearTimeout(timer);
  }, [activeView, detectVideoAspectRatio]);

  return aspectRatioData;
};

// Utility function to get CSS custom properties for dynamic sizing
export const getLayoutCSSProperties = (aspectRatioData: AspectRatioData) => {
  return {
    "--side-panel-width": `${aspectRatioData.sidePanelWidth}px`,
    "--bottom-panel-height": `${aspectRatioData.bottomPanelHeight}px`,
    "--video-aspect-ratio": aspectRatioData.aspectRatio.toString(),
    "--video-width": `${aspectRatioData.videoWidth}px`,
    "--video-height": `${aspectRatioData.videoHeight}px`,
  } as React.CSSProperties;
};
