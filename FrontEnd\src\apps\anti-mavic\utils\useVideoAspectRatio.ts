import { useState, useEffect, useCallback } from "react";
import { LAYOUT_CONFIG, CAMERA_SPECS } from "./config";

export interface AspectRatioData {
  aspectRatio: number;
  category: "ultraWide" | "wide" | "standard" | "square" | "portrait";
  sidePanelWidth: number;
  bottomPanelHeight: number;
  videoWidth: number;
  videoHeight: number;
}

export const useVideoAspectRatio = (
  activeView: "main" | "thermal",
  manualAspectRatio?: number
): AspectRatioData => {
  const [aspectRatioData, setAspectRatioData] = useState<AspectRatioData>({
    aspectRatio: 16 / 9,
    category: "wide",
    sidePanelWidth: 320,
    bottomPanelHeight: 200,
    videoWidth: 1920,
    videoHeight: 1080,
  });

  const calculateAspectRatioData = useCallback(
    (aspectRatio: number, width: number, height: number): AspectRatioData => {
      const {
        aspectRatioBreakpoints,
        panelAdjustments,
        defaultSidePanelWidth,
        defaultBottomPanelHeight,
        constraints,
      } = LAYOUT_CONFIG;

      // Determine aspect ratio category
      let category: AspectRatioData["category"];
      if (aspectRatio > aspectRatioBreakpoints.ultraWide) {
        category = "ultraWide";
      } else if (aspectRatio > aspectRatioBreakpoints.wide) {
        category = "wide";
      } else if (aspectRatio > aspectRatioBreakpoints.standard) {
        category = "standard";
      } else if (aspectRatio > aspectRatioBreakpoints.square) {
        category = "square";
      } else {
        category = "portrait";
      }

      // Calculate panel sizes based on category
      const adjustments = panelAdjustments[category];
      const calculatedSidePanelWidth = Math.round(
        defaultSidePanelWidth * adjustments.sidePanelWidthMultiplier
      );
      const calculatedBottomPanelHeight = Math.round(
        defaultBottomPanelHeight * adjustments.bottomPanelHeightMultiplier
      );

      // Apply constraints
      const sidePanelWidth = Math.max(
        constraints.sidePanelMinWidth,
        Math.min(constraints.sidePanelMaxWidth, calculatedSidePanelWidth)
      );

      const bottomPanelHeight = Math.max(
        constraints.bottomPanelMinHeight,
        Math.min(constraints.bottomPanelMaxHeight, calculatedBottomPanelHeight)
      );

      return {
        aspectRatio,
        category,
        sidePanelWidth,
        bottomPanelHeight,
        videoWidth: width,
        videoHeight: height,
      };
    },
    []
  );

  // Responsive design based on window size and common video aspect ratios
  const updateLayoutBasedOnScreenSize = useCallback(() => {
    const screenWidth = window.innerWidth;

    // Use manual aspect ratio if provided, otherwise estimate based on screen size
    let targetVideoAspectRatio: number;
    let estimatedWidth: number;
    let estimatedHeight: number;

    if (manualAspectRatio) {
      // Use manually set aspect ratio
      targetVideoAspectRatio = manualAspectRatio;
      estimatedWidth = screenWidth * 0.7;
      estimatedHeight = estimatedWidth / targetVideoAspectRatio;
    } else {
      // Use actual camera specifications for current view
      const cameraSpec = CAMERA_SPECS[activeView];
      targetVideoAspectRatio = cameraSpec.aspectRatio;
      estimatedWidth = screenWidth * 0.7;
      estimatedHeight = estimatedWidth / targetVideoAspectRatio;
    }

    const newData = calculateAspectRatioData(
      targetVideoAspectRatio,
      estimatedWidth,
      estimatedHeight
    );

    // Only update if the aspect ratio has changed significantly
    if (Math.abs(aspectRatioData.aspectRatio - targetVideoAspectRatio) > 0.1) {
      setAspectRatioData(newData);
      console.log(
        `📐 Layout updated: ${manualAspectRatio ? "Manual" : "Auto"} aspect ratio: ${targetVideoAspectRatio.toFixed(2)} (${newData.category})`
      );
    }
  }, [
    calculateAspectRatioData,
    aspectRatioData.aspectRatio,
    manualAspectRatio,
  ]);

  // Listen for window resize events
  useEffect(() => {
    // Initial calculation
    updateLayoutBasedOnScreenSize();

    // Listen for window resize
    const handleResize = () => {
      updateLayoutBasedOnScreenSize();
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [updateLayoutBasedOnScreenSize]);

  // Update when active view changes
  useEffect(() => {
    const timer = setTimeout(() => {
      updateLayoutBasedOnScreenSize();
    }, 300); // Small delay to allow view transition

    return () => clearTimeout(timer);
  }, [activeView, updateLayoutBasedOnScreenSize]);

  return aspectRatioData;
};

// Utility function to get CSS custom properties for dynamic sizing
export const getLayoutCSSProperties = (aspectRatioData: AspectRatioData) => {
  return {
    "--side-panel-width": `${aspectRatioData.sidePanelWidth}px`,
    "--bottom-panel-height": `${aspectRatioData.bottomPanelHeight}px`,
    "--video-aspect-ratio": aspectRatioData.aspectRatio.toString(),
    "--video-width": `${aspectRatioData.videoWidth}px`,
    "--video-height": `${aspectRatioData.videoHeight}px`,
  } as React.CSSProperties;
};
