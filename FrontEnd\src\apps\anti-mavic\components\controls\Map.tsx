import React from "react";
import { useTranslation } from "react-i18next";
import {
  APIProvider,
  Map as GoogleMap,
  Marker,
} from "@vis.gl/react-google-maps";

const Map = () => {
  const { t } = useTranslation("antimavic");

  // Hardcoded camera position for now
  const position = { lat: 50.45, lng: 30.52 };
  // default camera style satelite
  return (
    <div className="w-full h-full rounded-lg overflow-hidden">
      <APIProvider apiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}>
        <GoogleMap
          defaultCenter={position}
          defaultZoom={15}
          gestureHandling="greedy"
          mapId="8e0a97af9386fef"
          disableDefaultUI={true}
          mapTypeId={"satellite"}
        >
          <Marker position={position} />
        </GoogleMap>
      </APIProvider>
    </div>
  );
};

export default Map;
