import { useMap } from "@vis.gl/react-google-maps";

interface MapPolylineProps {
  coordinates: google.maps.LatLngLiteral[];
  strokeColor?: string;
  strokeWeight?: number;
  strokeOpacity?: number;
  onClick?: (e: google.maps.MapMouseEvent) => void;
}

export const MapPolyline = ({
  coordinates,
  strokeColor = "#3B82F6",
  strokeWeight = 2,
  strokeOpacity = 1,
  onClick,
}: MapPolylineProps) => {
  const map = useMap();

  if (!map || coordinates.length < 2) {
    return null;
  }

  // Create the polyline
  const polyline = new google.maps.Polyline({
    path: coordinates,
    strokeColor,
    strokeWeight,
    strokeOpacity,
    map,
  });

  // Add click handler if provided
  if (onClick) {
    polyline.addListener("click", onClick);
  }

  // Clean up on unmount
  return () => {
    polyline.setMap(null);
    google.maps.event.clearInstanceListeners(polyline);
  };
};
