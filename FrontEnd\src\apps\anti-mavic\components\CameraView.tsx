import { useTranslation } from "react-i18next";
import {
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { CAMERA_URLS } from "../utils/config";
import {
  MainCameraIcon,
  ThermalCameraIcon,
  UpIcon,
  DownIcon,
  LeftIcon,
  RightIcon,
  ResetCameraIcon,
  TargetIcon,
} from "../utils/icons";

type CameraType = "main" | "thermal";

export interface CameraViewRef {
  getMainCameraRef: () => React.RefObject<HTMLIFrameElement | null>;
  getThermalCameraRef: () => React.RefObject<HTMLIFrameElement | null>;
  getActiveView: () => CameraType;
}

interface CameraViewProps {
  className?: string;
}

const CameraView = forwardRef<CameraViewRef, CameraViewProps>(
  ({ className = "" }, ref) => {
    const { t } = useTranslation("antimavic");
    const [isLoading, setIsLoading] = useState({
      main: true,
      thermal: true,
    });
    const [activeView, setActiveView] = useState<CameraType>("main");
    const [isTransitioning, setIsTransitioning] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const mainCameraRef = useRef<HTMLIFrameElement>(null);
    const thermalCameraRef = useRef<HTMLIFrameElement>(null);

    // Expose refs and active view to parent component
    useImperativeHandle(ref, () => ({
      getMainCameraRef: () => mainCameraRef,
      getThermalCameraRef: () => thermalCameraRef,
      getActiveView: () => activeView,
    }));

    const switchView = (view: CameraType) => {
      if (activeView === view || isTransitioning) return;

      setIsTransitioning(true);
      setTimeout(() => {
        setActiveView(view);
        setTimeout(() => {
          setIsTransitioning(false);
        }, 300);
      }, 150);
    };

    useEffect(() => {
      const mainTimer = setTimeout(() => {
        setIsLoading((prev) => ({ ...prev, main: false }));
      }, 1500);

      const thermalTimer = setTimeout(() => {
        setIsLoading((prev) => ({ ...prev, thermal: false }));
      }, 1500);

      return () => {
        clearTimeout(mainTimer);
        clearTimeout(thermalTimer);
      };
    }, []);

    // Handlers for camera movement
    const handleCameraMove = (direction: "up" | "down" | "left" | "right") => {
      // This would send commands to the camera
      console.log(`Moving camera ${direction}`);
    };

    const handleResetCameraPosition = () => {
      // This would reset the camera to its default position
      console.log("Resetting camera position");
    };

    return (
      <div
        className={`w-full h-full flex items-center justify-center ${className}`}
      >
        <div
          ref={containerRef}
          className="relative w-full h-full bg-black rounded-lg overflow-hidden shadow-lg"
          style={{
            maxWidth: "100%",
            maxHeight: "100%",
            transition: "transform 300ms ease, aspect-ratio 300ms ease",
            transform: isTransitioning ? "scale(0.98)" : "scale(1)",
          }}
        >
          {/* Camera Type Selection Buttons - top-left */}
          <div className="absolute top-4 left-4 flex gap-2 z-30">
            <button
              onClick={() => switchView("main")}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center gap-1.5 ${
                activeView === "main"
                  ? "bg-black/70 text-white"
                  : "bg-black/40 text-gray-300 hover:bg-black/60"
              }`}
            >
              {MainCameraIcon}
              <span>{t("camera.mainView")}</span>
            </button>

            <button
              onClick={() => switchView("thermal")}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center gap-1.5 ${
                activeView === "thermal"
                  ? "bg-black/70 text-white"
                  : "bg-black/40 text-gray-300 hover:bg-black/60"
              }`}
            >
              {ThermalCameraIcon}
              <span>{t("camera.thermalView")}</span>
            </button>
          </div>

          {/* Main Camera */}
          <div
            className="absolute top-0 left-0 w-full h-full"
            style={{
              zIndex: 10,
              opacity: activeView === "main" ? 1 : 0,
              visibility: activeView === "main" ? "visible" : "hidden",
              transition: "opacity 0.3s ease, visibility 0.3s ease",
            }}
          >
            {isLoading.main && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/75 text-white z-10">
                {t("camera.loading")}
              </div>
            )}
            <iframe
              ref={mainCameraRef}
              src={CAMERA_URLS.main}
              className="w-full h-full border-0"
              title={t("camera.mainView")}
              loading="eager"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
              style={{
                opacity: isLoading.main ? 0 : 1,
                transition: "opacity 0.2s ease",
              }}
              allowFullScreen
            />
          </div>

          {/* Thermal Camera */}
          <div
            className="absolute top-0 left-0 w-full h-full"
            style={{
              zIndex: 10,
              opacity: activeView === "thermal" ? 1 : 0,
              visibility: activeView === "thermal" ? "visible" : "hidden",
              transition: "opacity 0.3s ease, visibility 0.3s ease",
            }}
          >
            {isLoading.thermal && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/75 text-white z-10">
                {t("camera.loading")}
              </div>
            )}
            <iframe
              ref={thermalCameraRef}
              src={CAMERA_URLS.thermal}
              className="w-full h-full border-0"
              title={t("camera.thermalView")}
              loading="eager"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
              style={{
                opacity: isLoading.thermal ? 0 : 1,
                transition: "opacity 0.2s ease",
              }}
              allowFullScreen
            />
          </div>

          {/* Center Target/Crosshair */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white/50 z-20">
            {TargetIcon}
          </div>

          {/* Camera Movement Controls (Joystick-like) - bottom right */}
          <div className="absolute bottom-4 right-4 z-30 bg-black/30 rounded-lg p-1 grid grid-cols-3 gap-1">
            <div></div>
            <button
              className="p-2 hover:bg-black/50 rounded text-white/70 hover:text-white transition-colors"
              onClick={() => handleCameraMove("up")}
              aria-label={t("controls.buttons.up")}
            >
              {UpIcon}
            </button>
            <div></div>

            <button
              className="p-2 hover:bg-black/50 rounded text-white/70 hover:text-white transition-colors"
              onClick={() => handleCameraMove("left")}
              aria-label={t("controls.buttons.left")}
            >
              {LeftIcon}
            </button>

            <button
              className="p-2 hover:bg-black/50 rounded text-white/70 hover:text-white transition-colors"
              onClick={handleResetCameraPosition}
              aria-label={t("camera.resetPosition")}
              title={t("camera.resetPosition")}
            >
              {ResetCameraIcon}
            </button>

            <button
              className="p-2 hover:bg-black/50 rounded text-white/70 hover:text-white transition-colors"
              onClick={() => handleCameraMove("right")}
              aria-label={t("controls.buttons.right")}
            >
              {RightIcon}
            </button>

            <div></div>
            <button
              className="p-2 hover:bg-black/50 rounded text-white/70 hover:text-white transition-colors"
              onClick={() => handleCameraMove("down")}
              aria-label={t("controls.buttons.down")}
            >
              {DownIcon}
            </button>
            <div></div>
          </div>

          {/* Reset Position Button - bottom center */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30">
            <button className="bg-black/40 hover:bg-black/60 text-white px-4 py-2 rounded-md text-sm transition-colors">
              {t("camera.resetPosition", "Reset Position")}
            </button>
          </div>
        </div>
      </div>
    );
  }
);

CameraView.displayName = "CameraView";

export default CameraView;
