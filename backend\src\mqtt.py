import asyncio
import inspect
import json
import time
import traceback
import uuid
from dataclasses import asdict
from functools import wraps
from typing import Any, Callable, Dict, Literal, Optional, TypedDict, Union

import gmqtt
from loguru import logger


async def on_error_callback_fastapi(detail: str = "") -> None:
    from fastapi import HTTPException

    raise HTTPException(status_code=503, detail=detail)


class MQTTMessage(TypedDict):
    id: str
    raspberry_id: Optional[str]
    sender_id: str
    sender_type: Literal["raspberry", "server"]
    data: dict


class MQTTError(Exception):
    """Base exception for MQTT-related errors"""

    pass


class ConnectionError(MQTTError):
    """Raised when connection to broker fails"""

    pass


class MessageError(MQTTError):
    """Raised when message processing fails"""

    pass


def make_param_instance(cls: Any, data: dict) -> Any:
    """
    Create an instance of a class from dictionary data.

    Args:
        cls: The target class
        data: Dictionary containing the data

    Returns:
        An instance of the target class
    """
    if cls is dict:
        return data

    try:
        # Get the parameters of the __init__ method
        init_params = cls.model_fields

        # Extract only the valid parameters
        valid_kwargs = {key: value for key, value in data.items() if key in init_params}

        # Create the instance with the filtered arguments
        return cls(**valid_kwargs)
    except Exception as e:
        logger.error(f"Failed to create instance of {cls.__name__}: {str(e)}")
        raise MessageError(f"Failed to create instance: {str(e)}")


def auto_dataclass_converter(func: Callable) -> Callable:
    """
    Decorator that automatically converts the first argument of a function to a dataclass.
    The dataclass type is taken from the type hint of the first argument.

    Args:
        func: The function to decorate

    Returns:
        Decorated function that handles dataclass conversion
    """
    # Inspect the function signature to get argument types
    signature = inspect.signature(func)
    params = signature.parameters
    # Get the first parameter
    param_name, param = next(iter(params.items()))

    @wraps(func)
    def wrapper(mqtt_msg: MQTTMessage, *args, **kwargs):
        instance = None
        if "content" in mqtt_msg:
            instance = make_param_instance(param.annotation, mqtt_msg["content"])
        return func(instance, *args, **kwargs)

    return wrapper


def auto_load_handlers(mqtt: "MQTTManager", data: dict) -> None:
    """
    Auto load all functions that start with 'on_' and register them as topic handlers.

    Args:
        mqtt: The MQTT manager instance
        data: Dictionary of function names and their implementations
    """
    logger.debug("Auto loading handlers")
    for f_name, func in data:
        if f_name.startswith("on_"):
            try:
                decorated_func = auto_dataclass_converter(func)
                topic = f_name.replace("on_", "")
                mqtt.register_decorator(topic)(decorated_func)
                logger.debug(f"Registered handler for topic: {topic} => {f_name}")
            except Exception as e:
                logger.error(f"Failed to register handler {f_name}: {str(e)}")


class MQTTManager:
    client: gmqtt.Client
    rasp_ignore_topics = ["responses", "status", "connect_group"]

    def __init__(
        self,
        host: str,
        port: int = 1883,
        prefix: str = "",
        username: Optional[str] = None,
        password: Optional[str] = None,
        client_id: Optional[str] = None,
        raspberry_id: Optional[str] = None,
        handle_error: bool = True,
        qos: int = 0,
        retry_interval: int = 5,
        max_retries: int = 3,
        on_connect_callback: Optional[Callable] = None,
    ):
        """
        Initialize the MQTT Manager.

        Args:
            host: MQTT broker host
            port: MQTT broker port
            username: Authentication username
            password: Authentication password
            client_id: Optional unique client identifier
            raspberry_id: Optional group identifier
            handle_error: Whether to handle errors internally
            qos: Quality of Service level (0, 1, or 2)
            retry_interval: Seconds between connection retries
            max_retries: Maximum number of connection attempts
        """
        self.client_id = client_id or uuid.uuid4().hex
        self.host = host
        self.port = port
        self.prefix = prefix
        self.raspberry_id = raspberry_id
        self.handle_error = handle_error
        self.qos = qos
        self.retry_interval = retry_interval
        self.max_retries = max_retries
        self._retry_count = 0
        self.on_connect_callback = on_connect_callback
        self.username = username
        self.password = password

        self.sender_type: Literal["raspberry", "server"] = "raspberry"
        if not raspberry_id:
            self.sender_type = "server"
            self.__on_error_callback = on_error_callback_fastapi

        self.handlers: Dict[str, Callable[[dict], Optional[dict]]] = {}
        self.pending_responses: Dict[str, asyncio.Future] = {}

    def _register_callbacks(self, client: gmqtt.Client):
        client.on_connect = self._on_connect
        client.on_message = self._on_message
        client.on_disconnect = self._on_disconnect
        client.on_subscribe = self._on_subscribe

    def _on_connect(self, client, flags, rc, properties):
        """Handle broker connection events."""
        if rc == 0:
            self._retry_count = 0
            logger.info("Connected to broker successfully")

            # Subscribe to topics
            client.subscribe(self.prefix + "/#", qos=self.qos)

            if self.on_connect_callback:
                self.on_connect_callback()

            if not self.raspberry_id:
                client.subscribe("responses", qos=self.qos)
        else:
            error_message = f"Connection failed with result code: {rc}"

            # Handle specific error codes
            if rc == 1:
                error_message = "Connection refused - incorrect protocol version"
            elif rc == 2:
                error_message = "Connection refused - invalid client identifier"
            elif rc == 3:
                error_message = "Connection refused - server unavailable"
            elif rc == 4:
                error_message = "Connection refused - bad username or password"
            elif rc == 5:
                error_message = "Connection refused - not authorised"

            logger.error(error_message)
            raise ConnectionError(error_message)

    def _on_disconnect(self, client, packet, exc=None):
        """Handle broker disconnection events."""
        logger.warning("Disconnected from broker")
        if exc:
            logger.error(f"Disconnect error: {str(exc)}")

    def _on_subscribe(self, client, mid, qos, properties):
        """Handle subscription confirmations."""
        pass

    async def _on_message(self, client, topic, payload, qos, properties):
        """Handle incoming messages."""
        try:
            prefix, topic = topic.split("/")
        except ValueError:
            logger.warning(f"Invalid topic: {topic}")
            return
        if prefix != self.prefix:
            # Ignore messages from other clients
            return
        try:
            message: MQTTMessage = json.loads(payload)
            # Ignore self-published messages
            if self.client_id == message.get("sender_id"):
                return

            # Handle pending responses
            if message.get("id") in self.pending_responses:
                future = self.pending_responses.pop(message["id"])
                future.set_result(message.get("content", {}))
                return

            # Handle regular messages
            handler = self.handlers.get(topic)

            # Ignore messages that don't have a handler
            if self.raspberry_id and topic in self.rasp_ignore_topics:
                return
            if not handler:
                logger.warning(f"No handler registered for topic: {topic}")
                return

            if self.raspberry_id == message.get("raspberry_id"):
                if self.handle_error:
                    try:
                        response = {"status": "ok", "data": handler(message)}
                    except Exception as e:
                        logger.error(f"Handler error for topic {topic}: {str(e)}")
                        traceback_str = traceback.format_exc()
                        print(traceback_str)
                        response = {"status": "err", "msg": str(e)}
                else:
                    response = {"status": "ok", "data": handler(message)}
                self.publish(
                    "responses",
                    response,
                    raspberry_id=self.raspberry_id,
                    sender_type=self.sender_type,
                    id=message["id"],
                )
            else:
                if self.sender_type == "server":
                    # This is messages from raspberry to server
                    if handler:
                        await handler(message)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode payload on topic {topic}: {str(e)}")
            print(traceback.format_exc())
            raise MessageError(f"Invalid JSON payload: {str(e)}")

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            print(traceback.format_exc())
            raise MessageError(f"Message processing failed: {str(e)}")
        finally:
            return 0

    def publish(
        self,
        topic: str,
        data: Union[dict, Any] = {},
        raspberry_id: Optional[str] = None,
        sender_type: Optional[str] = None,
        id: Optional[str] = None,
    ) -> str:
        """
        Publish a message to a topic.

        Args:
            topic: The topic to publish to
            data: Message payload (dict or dataclass)
            raspberry_id: Optional group identifier
            sender_type: Optional sender type
            id: Optional message identifier

        Returns:
            Message identifier
        """

        # Convert dataclass to dict if necessary
        if hasattr(data, "__dataclass_fields__"):
            data = asdict(data)

        id = id or uuid.uuid4().hex
        if "content" not in data:
            data = {"content": data}

        message = {
            "id": id,
            "sender_id": self.client_id,
            "sender_type": sender_type or self.sender_type,
            **data,
        }

        if raspberry_id:
            message["raspberry_id"] = str(raspberry_id)

        logger.debug(f"Publishing to {self.prefix}/{topic}")

        self.client.publish(f"{self.prefix}/{topic}", json.dumps(message), qos=self.qos)
        return id

    async def publish_with_resp(
        self,
        topic: str,
        data: Union[dict, Any] = {},
        raspberry_id: Optional[str] = None,
        timeout: int = 10,
    ) -> dict:
        """
        Publish a message and wait for a response.

        Args:
            topic: The topic to publish to
            data: Message payload
            raspberry_id: Optional group identifier
            timeout: Response timeout in seconds

        Returns:
            Response data

        Raises:
            asyncio.TimeoutError: If response not received within timeout
        """
        message_id = self.publish(topic, data, raspberry_id=raspberry_id)
        future = asyncio.get_event_loop().create_future()
        self.pending_responses[message_id] = future

        try:
            data = await asyncio.wait_for(future, timeout)
            if data["status"] == "err":
                raise Exception(data["msg"])
            return data["data"]
        except asyncio.TimeoutError:
            logger.error(
                f"[GROUP ID: {raspberry_id}] Timed out on topic {topic.upper()}"
            )
            self.pending_responses.pop(message_id, None)
            await self.__on_error_callback("Response timed out")
        except Exception as e:
            logger.error(f"Error waiting for response: {str(e)}")
            self.pending_responses.pop(message_id, None)
            await self.__on_error_callback(str(e))

    async def __on_error_callback(msg: str = None) -> None:
        pass

    async def connect(self):
        """
        Connect to the MQTT broker with retry mechanism.

        Raises:
            ConnectionError: If connection fails after max retries
        """

        # Initialize new MQTT client
        # BUG: I initialize a new MQTT client every time because of disconnect error!
        client = gmqtt.Client(self.client_id)
        logger.info(
            f"MQTT creds: {self.username}, {self.password} {self.host}:{self.port}"
        )
        if self.username and self.password:
            client.set_auth_credentials(self.username, self.password)

        self._register_callbacks(client)
        self.client = client
        try:
            await client.connect(self.host, self.port)
        except gmqtt.mqtt.handler.MQTTConnectError as e:
            print(f"{self.host}:{self.port}, {self.username}:{self.password}")
            logger.error(f"Failed to connect to broker: {str(e)}")

    async def disconnect(self):
        """Disconnect from the MQTT broker."""
        await self.client.disconnect()

    def register(self, topic, handler):
        self.register_decorator(topic)(handler)

    def register_decorator(self, topic: str) -> Callable:
        """
        Register a handler for a specific topic.

        Args:
            topic: The topic to handle

        Returns:
            Decorator for registering the handler
        """

        def decorator(handler: Callable[[dict], Optional[dict]]) -> Callable:
            self.handlers[topic] = handler
            logger.info(f"Handler registered for topic: {topic}")
            return handler

        return decorator

    @property
    def is_connected(self) -> bool:
        """Check if client is currently connected."""
        return self.client.is_connected


async def main(settings=None):
    if not settings:
        from src.conf.settings import settings
    mqtt = MQTTManager(
        host=settings.mqtt_broker_host,
        port=settings.mqtt_broker_port,
        username=settings.mqtt_username,
        password=settings.mqtt_password,
        prefix=settings.mqtt_prefix,
    )
    await mqtt.connect()
    while True:
        time.sleep(1)


if __name__ == "__main__":
    asyncio.run(main())
