export const getLatLngFromString = (
  value?: string | null
): google.maps.LatLngLiteral | undefined => {
  if (!value) {
    return undefined;
  }

  try {
    // Basic lat,lng string parsing
    const coords = value.split(/[,;]/).map((coord) => parseFloat(coord.trim()));

    if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
      const result = { lat: coords[0], lng: coords[1] };

      return result;
    } else {
    }
  } catch (e) {}

  return undefined;
};

export const getCoordinatesText = (
  latLng: google.maps.LatLngLiteral,
  isRounded = false
): string => {
  if (isRounded) {
    const roundedLat = Math.round(latLng.lat * 100000) / 100000;
    const roundedLng = Math.round(latLng.lng * 100000) / 100000;
    const result = `${roundedLat}, ${roundedLng}`;

    return result;
  }

  const result = `${latLng.lat}, ${latLng.lng}`;

  return result;
};
