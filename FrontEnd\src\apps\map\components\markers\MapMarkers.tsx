import { AdvancedMarker } from "@vis.gl/react-google-maps";
import { memo } from "react";
import { Move } from "lucide-react";
import type { Device } from "../../services/deviceService";
import type { Target } from "../../services/targetService";

interface DeviceMarkerProps {
  device: Device;
  onClick: (e: any) => void;
  isMoving?: boolean;
  draggable?: boolean;
  onDragEnd?: (e: google.maps.MapMouseEvent) => void;
}

interface TargetMarkerProps {
  target: Target;
  onClick: (e: any) => void;
  isMoving?: boolean;
  draggable?: boolean;
  onDragEnd?: (e: google.maps.MapMouseEvent) => void;
}

// Using memo to prevent unnecessary re-renders
export const DeviceMarker = memo(
  ({ device, onClick, isMoving, draggable, onDragEnd }: DeviceMarkerProps) => {
    const handleClick = (e: any) => {
      // Prevent default only for left click (button 0)
      if (e.domEvent && e.domEvent.button === 0) {
        e.domEvent.preventDefault();
        e.domEvent.stopPropagation();
      }
      onClick(e);
    };

    return (
      <AdvancedMarker
        position={{ lat: device.lat, lng: device.lng }}
        title={device.name || "Device"}
        onClick={handleClick}
        draggable={!!draggable}
        onDragEnd={onDragEnd}
      >
        <div className="flex flex-col items-center relative">
          <div
            className={`p-2 rounded-full text-white text-xs flex items-center justify-center shadow-md ${isMoving ? "cursor-move" : ""}`}
            style={{
              backgroundColor: device.color || "#1E90FF",
              opacity: device.state === "inactive" ? 0.5 : 1,
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-4 h-4"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            </svg>
          </div>
          {isMoving && (
            <div className="absolute -top-2 -right-2 bg-orange-500 rounded-full w-5 h-5 flex items-center justify-center shadow-md">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M5 9l-3 3 3 3"></path>
                <path d="M9 5l3-3 3 3"></path>
                <path d="M15 19l3 3 3-3"></path>
                <path d="M19 9l3 3-3 3"></path>
                <path d="M2 12h20"></path>
                <path d="M12 2v20"></path>
              </svg>
            </div>
          )}
          <div className="mt-1 px-1 bg-black/70 text-white text-xs rounded whitespace-nowrap">
            {device.name || "Device"}
          </div>
        </div>
      </AdvancedMarker>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function for memo
    // Return true if the props are the same (to prevent re-render)
    if (prevProps.device._id !== nextProps.device._id) return false;
    if (prevProps.device.name !== nextProps.device.name) return false;
    if (prevProps.device.lat !== nextProps.device.lat) return false;
    if (prevProps.device.lng !== nextProps.device.lng) return false;
    if (prevProps.device.color !== nextProps.device.color) return false;
    if (prevProps.device.state !== nextProps.device.state) return false;
    if (prevProps.device.mode !== nextProps.device.mode) return false;
    if (prevProps.device.type !== nextProps.device.type) return false;
    if (prevProps.isMoving !== nextProps.isMoving) return false;
    if (prevProps.draggable !== nextProps.draggable) return false;
    return true;
  }
);

// Using memo to prevent unnecessary re-renders
export const TargetMarker = memo(
  ({ target, onClick, isMoving, draggable, onDragEnd }: TargetMarkerProps) => {
    const handleClick = (e: any) => {
      // Fix for click handling - checking left click instead of right
      if (e.domEvent && e.domEvent.button === 0) {
        e.domEvent.preventDefault();
        e.domEvent.stopPropagation();
      }
      onClick(e);
    };

    return (
      <AdvancedMarker
        position={{ lat: target.lat, lng: target.lng }}
        title={target.name || "Target"}
        onClick={handleClick}
        draggable={!!draggable}
        onDragEnd={onDragEnd}
      >
        <div className="flex flex-col items-center relative">
          <div
            className={`p-2 bg-red-600 rounded-full text-white text-xs flex items-center justify-center shadow-md ${isMoving ? "cursor-move" : ""}`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-4 h-4"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
          </div>
          {isMoving && (
            <div className="absolute -top-2 -right-2 bg-orange-500 rounded-full w-5 h-5 flex items-center justify-center shadow-md">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M5 9l-3 3 3 3"></path>
                <path d="M9 5l3-3 3 3"></path>
                <path d="M15 19l3 3 3-3"></path>
                <path d="M19 9l3 3-3 3"></path>
                <path d="M2 12h20"></path>
                <path d="M12 2v20"></path>
              </svg>
            </div>
          )}
          <div className="mt-1 px-1 bg-black/70 text-white text-xs rounded whitespace-nowrap">
            {target.name || "Target"}
          </div>
        </div>
      </AdvancedMarker>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function for memo
    // Return true if the props are the same (to prevent re-render)
    if (prevProps.target._id !== nextProps.target._id) return false;
    if (prevProps.target.name !== nextProps.target.name) return false;
    if (prevProps.target.lat !== nextProps.target.lat) return false;
    if (prevProps.target.lng !== nextProps.target.lng) return false;
    if (prevProps.target.height !== nextProps.target.height) return false;
    if (prevProps.target.type !== nextProps.target.type) return false;
    if (prevProps.isMoving !== nextProps.isMoving) return false;
    if (prevProps.draggable !== nextProps.draggable) return false;
    return true;
  }
);
