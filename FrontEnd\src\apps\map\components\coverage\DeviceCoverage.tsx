import React, { useMemo, useEffect, useRef, useContext } from 'react';
import { useMapsLibrary, GoogleMapsContext } from '@vis.gl/react-google-maps';
import type { Device } from '../../services/deviceService';

interface DeviceCoverageProps {
  device: Device;
  isVisible?: boolean;
}

export const DeviceCoverage: React.FC<DeviceCoverageProps> = ({ 
  device, 
  isVisible = true 
}) => {
  const geometry = useMapsLibrary('geometry');
  const map = useContext(GoogleMapsContext)?.map;
  const polygonRef = useRef<google.maps.Polygon | null>(null);

  const polygonPath = useMemo(() => {
    if (!geometry || !device.lat || !device.lng || !isVisible) {
      return [];
    }

    const center = { lat: device.lat, lng: device.lng };
    const radius = (device.distance || 5) * 1000; // Convert km to meters
    const coverageAngle = device.coverageAngle || 120;
    const azimuth = device.azimuth || 0;

    // If coverage angle is 360 or more, create a full circle
    if (coverageAngle >= 360) {
      const pointCount = 72;
      const path: google.maps.LatLngLiteral[] = [];
      
      for (let i = 0; i <= pointCount; i++) {
        const heading = (360 / pointCount) * i;
        const point = geometry.spherical.computeOffset(center, radius, heading);
        path.push(point.toJSON());
      }
      
      return path;
    }

    // Create arc for partial coverage
    const pointCount = Math.max(Math.floor(coverageAngle / 5), 8); // At least 8 points
    const startAngle = azimuth - coverageAngle / 2;
    const endAngle = azimuth + coverageAngle / 2;
    
    const path: google.maps.LatLngLiteral[] = [center]; // Start from center
    
    for (let i = 0; i <= pointCount; i++) {
      const angle = startAngle + (endAngle - startAngle) * (i / pointCount);
      const point = geometry.spherical.computeOffset(center, radius, angle);
      path.push(point.toJSON());
    }
    
    path.push(center); // Close the arc back to center
    
    return path;
  }, [geometry, device, isVisible]);

  // Create and manage polygon
  useEffect(() => {
    if (!map || !isVisible || polygonPath.length === 0) {
      if (polygonRef.current) {
        polygonRef.current.setMap(null);
        polygonRef.current = null;
      }
      return;
    }

    // Create polygon if it doesn't exist
    if (!polygonRef.current) {
      polygonRef.current = new google.maps.Polygon({
        fillColor: device.color || '#1E90FF',
        fillOpacity: 0.2,
        strokeColor: device.color || '#1E90FF',
        strokeWeight: 2,
        strokeOpacity: 0.6,
      });
    }

    // Update polygon properties
    polygonRef.current.setOptions({
      fillColor: device.color || '#1E90FF',
      strokeColor: device.color || '#1E90FF',
    });

    // Set the path
    polygonRef.current.setPaths(polygonPath);
    
    // Add to map
    polygonRef.current.setMap(map);

    return () => {
      if (polygonRef.current) {
        polygonRef.current.setMap(null);
      }
    };
  }, [map, polygonPath, device.color, isVisible]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (polygonRef.current) {
        polygonRef.current.setMap(null);
        polygonRef.current = null;
      }
    };
  }, []);

  return null;
}; 