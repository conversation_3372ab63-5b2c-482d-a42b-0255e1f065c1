import datetime

from loguru import logger
from src.apps.utils import serialize_mongo_document
from src.conf.loader import MotorManager, get_motor_manager
from src.mqtt import MQTTManager
from src.services.redis import RedisManager
from src.services.tsdb.client import TSDBClient
from src.services.tsdb.utils import PointsManager

from .models.unit import UnitDB

db: MotorManager = get_motor_manager()


def now_tz_utc():
    return datetime.datetime.now(tz=datetime.timezone.utc)


class Controller:
    def __init__(self, mqtt: MQTTManager, redis: RedisManager, ts_db: TSDBClient):
        self.redis = redis
        self.mqtt = mqtt
        self.ts_db = ts_db
        self.points_manager = PointsManager(ts_db)
        self.handlers = {
            "status": self.on_state,
            "connect_group": self.on_connect_group,
            "events": self.on_events,
        }

    def start(self):
        self.__subscribe()

    def __subscribe(self):
        for topic, handler in self.handlers.items():
            self.mqtt.register(topic, handler)

    async def on_state(self, message):
        raspberry_id = message["raspberry_id"]
        content = message["content"]
        self.redis.group.set_json(raspberry_id, content)

        # TODO restore saving to influx
        # self.points_manager.make_points(raspberry_id, content)
        # self.points_manager.write_unique_points()

    async def on_connect_group(self, message):
        raspberry_id = message["raspberry_id"]
        group = await UnitDB.find_one({"raspberry_id": raspberry_id}, fetch_links=True)
        if not group:
            logger.warning(f"Group {raspberry_id} not found")
        group = group.model_dump()
        sdrs = {device["data_key"]: device for device in group["devices"]}
        sdrs = serialize_mongo_document(sdrs)
        leds = serialize_mongo_document(group["relay_leds"])
        self.mqtt.publish(
            "insert_initial_state",
            {"sdrs": sdrs, "relay_leds": leds},
            raspberry_id=raspberry_id,
        )

    async def on_events(self, message):
        """
        content: {"event": "stop/start", "event_id": int},
        """
        content = message["content"]
        print(content)
        return
        match content["event"]:
            case "start":
                stm = DeviceEvents.update(
                    {
                        DeviceEvents.started_on: now_tz_utc(),
                        DeviceEvents.status: DeviceEvents.Statuses.active,
                    }
                )
        match content["event"]:
            case "stop":
                stm = DeviceEvents.update(
                    {
                        DeviceEvents.stopped_on: now_tz_utc(),
                        DeviceEvents.status: DeviceEvents.Statuses.finished,
                    }
                )
        print(stm)
        if stm:
            print("yes")
            stm.where(DeviceEvents.id == content["event_id"]).run_sync()

    def stop_devices(self, devices: list[dict]):
        for device in devices:
            print("Stop Device", device["id"], "Group", device["group"]["raspberry_id"])
            self.mqtt.publish(
                "stop",
                {"sdr_type": device["ampf_type"]["name"]},
                raspberry_id=device["group"]["raspberry_id"],
            )

    async def stop_devices_for_station(self, station_id):
        groups = await db.pro.station_groups.find({"station": station_id}).to_list()
        devices = []
        for g in groups:
            devices.append(g.devices)
        self.stop_devices(devices)
