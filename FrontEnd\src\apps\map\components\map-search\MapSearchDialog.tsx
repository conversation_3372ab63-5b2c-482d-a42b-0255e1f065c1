import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMapsLibrary } from "@vis.gl/react-google-maps";
import { X, Search, MapPin } from "lucide-react";

import { MapSearchPlaceAutocomplete } from "./MapSearchPlaceAutocomplete";
import { CoordinatesInput } from "./CoordinatesInput";
import {
  getLatLngFromString,
  isValidCoordinatesString,
} from "../../utils/coordinateUtils";

export interface SearchResult {
  position: google.maps.LatLngLiteral;
  title?: string;
  description?: string;
  zoom?: number;
}

export interface MapSearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSearchResult: (result: SearchResult) => void;
}

export const MapSearchDialog = ({
  isOpen,
  onClose,
  onSearchResult,
}: MapSearchDialogProps) => {
  const { t } = useTranslation("map");
  const [coordinatesValue, setCoordinatesValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const geocodingLib = useMapsLibrary("geocoding");
  const geocoderRef = useRef<google.maps.Geocoder | null>(null);

  // Initialize geocoder
  useEffect(() => {
    if (geocodingLib) {
      try {
        geocoderRef.current = new geocodingLib.Geocoder();
      } catch (error) {
        console.error("Failed to create Geocoder:", error);
      }
    }
  }, [geocodingLib]);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setCoordinatesValue("");
    }
  }, [isOpen]);

  const handlePlaceSelect = useCallback(
    async (place: google.maps.places.AutocompletePrediction) => {
      if (!place.place_id || !geocoderRef.current) {
        return;
      }

      try {
        setIsLoading(true);

        // Use async/await approach like the old working implementation
        const response = await geocoderRef.current.geocode({
          placeId: place.place_id,
        });

        const location = response.results.at(0)?.geometry?.location;

        if (location) {
          const searchResult: SearchResult = {
            position: location.toJSON(),
            title: place.structured_formatting.main_text,
            description: place.structured_formatting.secondary_text,
            zoom: 15,
          };

          onSearchResult(searchResult);
          onClose();
        }
      } catch (error) {
        console.error("Error geocoding place:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [onSearchResult, onClose]
  );

  const handleCoordinatesSearch = useCallback(async () => {
    if (!coordinatesValue.trim()) {
      return;
    }

    if (!geocodingLib) {
      return;
    }

    if (!geocoderRef.current) {
      return;
    }

    try {
      setIsLoading(true);

      const latLng = getLatLngFromString(coordinatesValue);

      if (latLng) {
        // Use async/await approach like the old working implementation
        const result = await geocoderRef.current.geocode({
          location: latLng,
        });

        const searchResult: SearchResult = {
          position: latLng.toJSON(),
          title: "Custom Location",
          description: `${latLng.lat().toFixed(6)}, ${latLng.lng().toFixed(6)}`,
          zoom: 18,
        };

        const item = result.results?.at(0);
        if (item) {
          // Use the formatted address if available
          searchResult.title = item.formatted_address || "Custom Location";
        }

        onSearchResult(searchResult);
        onClose();
      }
    } catch (error) {
      console.error("Error parsing coordinates:", error);
    } finally {
      setIsLoading(false);
    }
  }, [coordinatesValue, onSearchResult, onClose, geocodingLib]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && isValidCoordinatesString(coordinatesValue)) {
        handleCoordinatesSearch();
      }
    },
    [coordinatesValue, handleCoordinatesSearch]
  );

  if (!isOpen) return null;

  const isCoordinatesValid = isValidCoordinatesString(coordinatesValue);

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-16">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Dialog */}
      <div className="relative bg-white dark:bg-[--military-dark] rounded-lg shadow-xl border border-gray-300 dark:border-[--military-accent] w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-[--military-accent]">
          <div className="flex items-center">
            <Search className="h-5 w-5 text-gray-500 dark:text-[--sand-light] mr-2" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-[--sand-light]">
              {t("search")}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:text-[--sand-light]/70 dark:hover:text-[--sand-light]"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {/* Place search */}
          <div>
            <MapSearchPlaceAutocomplete
              label={t("search_by_place")}
              placeholder={t("enter_place_name")}
              onChange={handlePlaceSelect}
            />
          </div>

          {/* Divider */}
          <div className="flex items-center">
            <div className="flex-1 border-t border-gray-300 dark:border-[--military-accent]"></div>
            <span className="px-3 text-sm text-gray-500 dark:text-[--sand-light]/70">
              {t("or")}
            </span>
            <div className="flex-1 border-t border-gray-300 dark:border-[--military-accent]"></div>
          </div>

          {/* Coordinates search */}
          <div>
            <CoordinatesInput
              label={t("search_by_coordinates")}
              placeholder={t("enter_coordinates")}
              value={coordinatesValue}
              onChange={setCoordinatesValue}
            />
          </div>

          {/* Search button for coordinates */}
          <div className="flex justify-end">
            <button
              onClick={handleCoordinatesSearch}
              onKeyPress={handleKeyPress}
              disabled={!isCoordinatesValid || isLoading}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                isCoordinatesValid && !isLoading
                  ? "bg-blue-500 hover:bg-blue-600 text-white"
                  : "bg-gray-300 dark:bg-[--military-light] text-gray-500 dark:text-[--sand-light]/50 cursor-not-allowed"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  {t("searching")}
                </div>
              ) : (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2" />
                  {t("go_to_location")}
                </div>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
