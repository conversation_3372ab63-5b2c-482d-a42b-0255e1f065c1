import { useEffect, useRef } from 'react';
import { useMap } from '@vis.gl/react-google-maps';
import { useDeepStateMapQuery } from '../../hooks/useDeepStateMapQuery';

interface ConflictLinesProps {
  isVisible?: boolean;
}

export const ConflictLines = ({ isVisible = true }: ConflictLinesProps) => {
  const map = useMap();
  const { data: deepStateData, isLoading, isError } = useDeepStateMapQuery();
  const polygonsRef = useRef<google.maps.Polygon[]>([]);

  // Clear existing polygons
  const clearPolygons = () => {
    polygonsRef.current.forEach(polygon => {
      polygon.setMap(null);
    });
    polygonsRef.current = [];
  };

  // Create and display polygons
  useEffect(() => {
    if (!map || !deepStateData?.features || !isVisible) {
      clearPolygons();
      return;
    }

    // Clear existing polygons first
    clearPolygons();

    // Create new polygons
    const newPolygons = deepStateData.features
      .filter(feature => feature.coordinates && feature.coordinates.length > 0)
      .map(feature => {
        const polygon = new google.maps.Polygon({
          paths: feature.coordinates,
          fillColor: feature.properties?.fillColor || '#ff0000',
          fillOpacity: feature.properties?.fillOpacity || 0.3,
          strokeColor: feature.properties?.strokeColor || '#ff0000',
          strokeWeight: feature.properties?.strokeWeight || 2,
          strokeOpacity: feature.properties?.strokeOpacity || 0.8,
          clickable: false,
          zIndex: 500, // Lower than measuring polylines (2000) to ensure measuring is always on top
        });

        polygon.setMap(map);
        return polygon;
      });

    polygonsRef.current = newPolygons;

    // Cleanup function
    return () => {
      clearPolygons();
    };
  }, [map, deepStateData, isVisible]);

  // Handle visibility changes
  useEffect(() => {
    polygonsRef.current.forEach(polygon => {
      polygon.setVisible(isVisible);
    });
  }, [isVisible]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearPolygons();
    };
  }, []);

  if (isLoading) {
    console.log('Loading conflict lines data...');
  }

  if (isError) {
    console.error('Error loading conflict lines data');
  }

  // This component doesn't render anything visible - it manages Google Maps polygons directly
  return null;
}; 