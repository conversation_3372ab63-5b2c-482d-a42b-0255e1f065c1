
from beanie import Document

from .amplifier import *
from .unit import *
from .modulation import *
from .preset_screen import *
from .presets import *
from .quick_start import *
from .station import *


def get_models() -> list:
    models = []
    for obj in globals().values():
        # if isinstance(obj, type):  # Check if the object is a class
        if issubclass(obj, Document):
            
            models.append(obj)
    return models

