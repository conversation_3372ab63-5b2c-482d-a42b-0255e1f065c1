from datetime import datetime
from pathlib import Path

import pytz
from pydantic_settings import BaseSettings, SettingsConfigDict

# DOTENV = str(Path(__file__).parent.parent.parent.parent)
DOTENV = Path(__file__).parent.parent.parent.parent / ".env"


def load_settings():
    settings = None
    dirs = None

    def container():
        nonlocal settings, dirs
        if not settings:
            print("Loading settings")
            settings = Settings()
            # pprint(settings)

            dirs = Dirs()
        return settings, dirs

    return container()


class Settings(BaseSettings):
    vite_mode: str = "dev"
    login_required: bool = False
    raspberry_port: int = 9000
    app_secret: str = "!secret"

    # MQTT Configuration
    mqtt_broker_host: str = "localhost"
    mqtt_broker_port: int = 1883
    mqtt_username: str | None = "phantom_admin"
    mqtt_password: str | None = "phantom_admin_ASAP"
    mqtt_prefix: str = "dev"

    # CORS Origins
    backend_cors_origins_string: str = "localhost,127.0.0.1"

    # Server Configuration
    server_addr: str = "127.0.0.1"
    server_port: int = 8080

    # MongoDB connection URI
    mongo_host: str = "localhost"
    mongo_port: int = 27017

    # Admin Email
    admin_email: str = "<EMAIL>"
    admin_email_app_password: str = "lSICzI9rPUr9iojc"

    # Security
    secret_key: str = "<EMAIL>"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 300

    # Redis
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_password: str = "strong_asap_redis_password!"

    # EcoFlow
    ecoflow_access_key: str | None = None
    ecoflow_secret_key: str | None = None

    timezone: str = "Europe/Kyiv"

    # Chat base url
    chat_base_url: str = "http://localhost:5050/api"

    # TS DB
    influxdb_con_url: str = "http://127.0.0.1:8181"
    influxdb_token: str = "token"
    influxdb_org: str = "asap"
    influxdb_database: str = "tsdb"
    influxdb_write_precision: str = "s"

    model_config = SettingsConfigDict(
        env_file=DOTENV, env_file_encoding="utf-8", extra="allow"
    )

    @property
    def mongo_uri(self) -> str:
        return f"mongodb://{self.mongo_host}:{self.mongo_port}"

    @property
    def current_tz(self) -> datetime.tzinfo:
        return pytz.timezone(self.timezone)

    @property
    def backend_cors_origins(self):
        return list(filter(lambda x: x, self.backend_cors_origins_string.split(",")))

    @property
    def env_type(self) -> str:
        return self.vite_mode


class Dirs:
    def __init__(self):
        self.base = Path(__file__).parent.parent.parent / "src"
        self.app = self.base / "app"
        self.app_templates = self.app / "templates"
        self.media_root = self.base / "uploads"
        self.signal_files = self.base / "signal_files"
        self.logs = self.base.parent / "logs"


settings, dirs = load_settings()
print(
    f"Mqtt: {settings.mqtt_broker_host}:{settings.mqtt_broker_port} {settings.mqtt_prefix}"
)

print(f"TSDB: {settings.influxdb_con_url}, TS_TOKEN: {settings.influxdb_token}")
print(f"Redis: {settings.redis_host}:{settings.redis_port}")
print(f"ENV type: {settings.env_type}")
