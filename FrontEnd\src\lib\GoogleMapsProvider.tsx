import { APIProvider } from "@vis.gl/react-google-maps";
import { useTranslation } from "react-i18next";

interface GoogleMapsProviderProps {
  children: React.ReactNode;
}

export const GoogleMapsProvider = ({ children }: GoogleMapsProviderProps) => {
  const { t, i18n } = useTranslation("map");

  const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

  // If no API key, render children without provider (for non-map pages)
  if (!GOOGLE_MAPS_API_KEY) {
    console.warn(
      "Google Maps API key not found. Map functionality will be limited."
    );
    return <>{children}</>;
  }

  // Get language and region with fallbacks
  const language =
    i18n.language === "ua" || i18n.language === "uk" ? "uk" : "en";
  const region = language === "uk" ? "UA" : "US";

  return (
    <APIProvider
      apiKey={GOOGLE_MAPS_API_KEY}
      language={language}
      region={region}
      libraries={["places", "drawing", "geometry", "geocoding"]}
    >
      {children}
    </APIProvider>
  );
};
