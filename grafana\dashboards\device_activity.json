{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"datasource": {"type": "influxdb", "uid": "P69C90A99EE20C552"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["status"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"dataset": "iox", "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT * FROM \"sdrs\" WHERE \"time\" >= $__timeFrom AND \"time\" <= $__timeTo AND sdr_type = '$sdr_type' AND raspberry_id = '$raspberry_id'", "refId": "A", "sql": {"columns": [{"parameters": [{"name": "*", "type": "functionParameter"}], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}]}, "table": "sdrs"}], "title": "SDR", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "P69C90A99EE20C552"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"dataset": "iox", "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT * FROM \"relay\" WHERE \"time\" >= $__timeFrom AND \"time\" <= $__timeTo AND raspberry_id = '$raspberry_id'", "refId": "A", "sql": {"columns": [{"parameters": [{"name": "*", "type": "functionParameter"}], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}]}, "table": "relay"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "P69C90A99EE20C552"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"dataset": "iox", "datasource": {"type": "influxdb", "uid": "P69C90A99EE20C552"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT * FROM \"temperature\" WHERE \"time\" >= $__timeFrom AND \"time\" <= $__timeTo AND raspberry_id = $raspberry_id", "refId": "A", "sql": {"columns": [{"parameters": [{"name": "*", "type": "functionParameter"}], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}]}, "table": "temperature"}], "title": "Temperature", "type": "timeseries"}], "preload": false, "schemaVersion": 41, "tags": [], "templating": {"list": [{"allowCustomValue": false, "current": {"text": "1", "value": "1"}, "definition": "select raspberry_id from temperature group by raspberry_id", "name": "raspberry_id", "options": [], "query": {"query": "select raspberry_id from temperature group by raspberry_id", "refId": "InfluxVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"allowCustomValue": false, "current": {"text": "_1000_3000", "value": "_1000_3000"}, "definition": "select sdr_type from sdrs group by sdr_type", "name": "sdr_type", "options": [], "query": {"query": "select sdr_type from sdrs group by sdr_type", "refId": "InfluxVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Device Activity", "uid": "ceiaxcd5dyl1cb", "version": 16}