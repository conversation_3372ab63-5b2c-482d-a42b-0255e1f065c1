import { GripVertical } from "lucide-react";

interface ResizeHandleProps {
  onMouseDown: (e: React.MouseEvent) => void;
  orientation?: 'vertical' | 'horizontal';
  className?: string;
}

export const ResizeHandle = ({ 
  onMouseDown, 
  orientation = 'vertical', 
  className = "" 
}: ResizeHandleProps) => {
  return (
    <div 
      className={`cursor-col-resize hover:bg-blue-500 transition-colors bg-[--gray-3] dark:bg-[--gray-7] flex items-center justify-center ${
        orientation === 'vertical' ? 'w-1' : 'h-1'
      } ${className}`}
      onMouseDown={onMouseDown}
    >
      <GripVertical className="h-4 w-4 text-[--gray-5] dark:text-[--gray-4]" />
    </div>
  );
};

interface ColumnResizeHandleProps {
  onMouseDown: (e: React.MouseEvent) => void;
  className?: string;
}

export const ColumnResizeHandle = ({ onMouseDown, className = "" }: ColumnResizeHandleProps) => {
  return (
    <div 
      className={`w-1 cursor-col-resize hover:bg-blue-500 transition-colors flex items-center justify-center ${className}`}
      onMouseDown={onMouseDown}
    >
      <div className="w-0.5 h-4 bg-[--gray-4] dark:bg-[--gray-5]" />
    </div>
  );
}; 