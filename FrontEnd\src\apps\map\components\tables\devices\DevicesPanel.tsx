import { useState, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Search, X } from "lucide-react";
import type { Device } from "../../../services/deviceService";
import { useResizable } from "../../../hooks/useResizable";
import { useResizableColumns } from "../../../hooks/useResizableColumns";
import { ResizeHandle, ColumnResizeHandle } from "../ResizeHandle";

interface DevicesPanelProps {
  devices: Device[];
  isOpen: boolean;
  onClose: () => void;
  onWidthChange?: (width: number) => void;
  className?: string;
}

export const DevicesPanel = ({ devices, isOpen, onClose, onWidthChange, className = "" }: DevicesPanelProps) => {
  const { t } = useTranslation("map");
  const [searchQuery, setSearchQuery] = useState("");

  // Panel resizing
  const { width: panelWidth, isResizing: isPanelResizing, handleMouseDown: handlePanelResize } = useResizable({
    initialWidth: 400,
    minWidth: 300,
    maxWidth: 800,
  });

  // Notify parent about width changes
  useEffect(() => {
    if (onWidthChange && isOpen) {
      onWidthChange(panelWidth);
    }
  }, [panelWidth, isOpen, onWidthChange]);

  // Column resizing
  const {
    columnWidths,
    isResizing: isColumnResizing,
    resizingColumn,
    containerRef,
    handleMouseDown: handleColumnResize,
    getColumnStyle,
  } = useResizableColumns({
    initialWidths: [45, 25, 30], // percentages for three columns
    minWidth: 15,
  });

  const filteredDevices = useMemo(() => {
    if (!searchQuery.trim()) return devices;
    
    const query = searchQuery.toLowerCase();
    return devices.filter(device =>
      device.name?.toLowerCase().includes(query) ||
      device.type?.toLowerCase().includes(query) ||
      device.azimuth?.toString().includes(query)
    );
  }, [devices, searchQuery]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed left-64 top-0 h-full bg-[--gray-9] dark:bg-[--gray-8] shadow-lg z-30 border-r border-[--gray-3] dark:border-[--gray-7] flex ${className}`}
      style={{ width: panelWidth }}
    >
      {/* Main panel content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-[--gray-3] dark:border-[--gray-7]">
          <h2 className="text-lg font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("devices")}
          </h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-[--gray-8] dark:hover:bg-[--gray-7] text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
            aria-label={t("close")}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-[--gray-3] dark:border-[--gray-7]">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[--gray-5] dark:text-[--gray-5]" />
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder={t("table.search_placeholder")}
              className="w-full pl-10 pr-3 py-2 bg-[--gray-8] dark:bg-[--gray-9] border border-[--gray-3] dark:border-[--gray-6] rounded-md text-[--gray-1] dark:text-[--gray-1] placeholder-[--gray-5] dark:placeholder-[--gray-5] text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Table Container */}
        <div className="flex-1 overflow-hidden" ref={containerRef}>
          {/* Table Header */}
          <div className="flex px-4 py-3 border-b border-[--gray-3] dark:border-[--gray-7] bg-[--gray-8] dark:bg-[--gray-8]">
            <div style={getColumnStyle(0)} className="text-xs font-medium text-[--gray-2] dark:text-[--gray-3] uppercase tracking-wider pr-2">
              {t("table.group_name")}
            </div>
            {/* Resize handle */}
            <ColumnResizeHandle onMouseDown={(e) => handleColumnResize(e, 0)} />
            <div style={getColumnStyle(1)} className="text-xs font-medium text-[--gray-2] dark:text-[--gray-3] uppercase tracking-wider px-2">
              {t("azimuth")}
            </div>
            {/* Resize handle */}
            <ColumnResizeHandle onMouseDown={(e) => handleColumnResize(e, 1)} />
            <div style={getColumnStyle(2)} className="text-xs font-medium text-[--gray-2] dark:text-[--gray-3] uppercase tracking-wider pl-2">
              {t("table.target")}
            </div>
          </div>

          {/* Table Body */}
          <div className="h-full overflow-y-auto">
            {filteredDevices.length > 0 ? (
              filteredDevices.map((device, index) => (
                <div
                  key={device._id || index}
                  className="flex px-4 py-3 border-b border-[--gray-3] dark:border-[--gray-7] hover:bg-[--gray-8] dark:hover:bg-[--gray-7] transition-colors duration-150 cursor-pointer"
                >
                  <div style={getColumnStyle(0)} className="pr-2">
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-[--gray-1] dark:text-[--gray-1] truncate">
                        {device.name || t("table.no_name")}
                      </span>
                      <span className="text-xs text-[--gray-4] dark:text-[--gray-4] truncate">
                        {device.type || t("table.unknown_type")}
                      </span>
                    </div>
                  </div>
                  <div className="w-1" /> {/* Spacer for resize handle */}
                  <div style={getColumnStyle(1)} className="px-2">
                    <div className="text-sm text-[--gray-1] dark:text-[--gray-1] truncate">
                      {device.azimuth !== undefined ? `${device.azimuth}°` : "—"}
                    </div>
                  </div>
                  <div className="w-1" /> {/* Spacer for resize handle */}
                  <div style={getColumnStyle(2)} className="pl-2">
                    <div className="text-sm text-[--gray-1] dark:text-[--gray-1] truncate">
                      —
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="flex items-center justify-center py-12">
                <span className="text-sm text-[--gray-4] dark:text-[--gray-4]">
                  {t("table.no_rows")}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Resize handle for panel */}
      <ResizeHandle onMouseDown={handlePanelResize} />
    </div>
  );
}; 