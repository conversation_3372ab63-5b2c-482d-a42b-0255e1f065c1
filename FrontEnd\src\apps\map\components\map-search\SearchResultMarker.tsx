import { Advanced<PERSON>ark<PERSON>, Pin } from "@vis.gl/react-google-maps";
import { MapPin } from "lucide-react";

export interface SearchResult {
  position: google.maps.LatLngLiteral;
  title?: string;
  description?: string;
}

export interface SearchResultMarkerProps {
  searchResult: SearchResult;
  onClick?: () => void;
  onClose?: () => void;
}

export const SearchResultMarker = ({
  searchResult,
  onClick,
  onClose,
}: SearchResultMarkerProps) => {
  return (
    <>
      <AdvancedMarker
        position={searchResult.position}
        onClick={onClick}
        title={searchResult.title || "Search Result"}
      >
        <div className="relative">
          {/* Custom marker with search icon */}
          <div className="bg-blue-500 text-white p-2 rounded-full shadow-lg border-2 border-white">
            <MapPin className="h-6 w-6" />
          </div>

          {/* Close button */}
          {onClose && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
              title="Remove search result"
            >
              ×
            </button>
          )}
        </div>
      </AdvancedMarker>

      {/* Info window content if needed */}
      {searchResult.title && (
        <div className="absolute z-10 bg-white dark:bg-[--military-dark] border border-gray-300 dark:border-[--military-accent] rounded-md shadow-lg p-2 text-sm max-w-xs pointer-events-none">
          <div className="font-medium text-gray-900 dark:text-[--sand-light]">
            {searchResult.title}
          </div>
          {searchResult.description && (
            <div className="text-gray-600 dark:text-[--sand-light]/70 mt-1">
              {searchResult.description}
            </div>
          )}
        </div>
      )}
    </>
  );
};
