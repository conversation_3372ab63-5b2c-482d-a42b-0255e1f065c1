import axiosClient from "../../../lib/axiosClient";

export interface Device {
  _id?: string;
  lat: number;
  lng: number;
  name?: string;
  type?: string;
  distance?: number;
  coverageAngle?: number;
  azimuth?: number;
  verticalAngleLimit?: number;
  color?: string;
  mode?: "suppression" | "normal";
  state?: "active" | "inactive";
  // Add other fields as needed
}

export const deviceService = {
  // Get all devices
  async getAllDevices(): Promise<Device[]> {
    const response = await axiosClient.get<Device[]>("/map/units/");
    return response.data || [];
  },

  // Create a new device
  async createDevice(device: Omit<Device, "_id">): Promise<Device | null> {
    const response = await axiosClient.post<Device>("/map/units/", device);
    return response.data || null;
  },

  // Get device by ID
  async getDeviceById(_id: string): Promise<Device | null> {
    const response = await axiosClient.get<Device>(`/map/units/${_id}`);
    return response.data || null;
  },

  // Update device
  async updateDevice(
    _id: string,
    device: Partial<Device>
  ): Promise<Device | null> {
    // Create a clean version of the device data without _id
    const { _id: deviceId, ...cleanDeviceData } = device;

    const response = await axiosClient.put<Device>(
      `/map/units/${_id}`,
      cleanDeviceData
    );

    // If we don't get back a full device object from the API, create a merged one
    if (response.data) {
      // Make sure all fields from the update are in the returned data
      return {
        ...(response.data || {}),
        ...cleanDeviceData,
        _id: _id,
      };
    }

    return null;
  },

  // Delete device
  async deleteDevice(_id: string): Promise<boolean> {
    try {
      const response = await axiosClient.delete(`/map/units/${_id}`);
      const success = response.status === 200 || response.status === 204;
      return success;
    } catch (error) {
      console.error(`Error deleting device with ID ${_id}:`, error);
      return false;
    }
  },
};
