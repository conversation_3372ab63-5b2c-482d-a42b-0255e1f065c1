import { useTranslation } from "react-i18next";
import { Edit, Trash, Settings } from "lucide-react";
import type { Device } from "../../services/deviceService";

export interface DeviceContextMenuProps {
  open: boolean;
  device: Device | null;
  position: { x: number; y: number };
  onEdit?: (device: Device) => void;
  onDelete?: (device: Device) => void;
  onControl?: (device: Device) => void;
  onMove?: (device: Device) => void;
  onDeactivate?: (device: Device) => void;
  onClose?: () => void;
}

export const DeviceContextMenu = ({
  open,
  device,
  position,
  onEdit,
  onDelete,
  onControl,
  onMove,
  onDeactivate,
  onClose,
}: DeviceContextMenuProps) => {
  const { t } = useTranslation("map");

  if (!open || !device) return null;

  const handleAction = (callback?: (device: Device) => void) => {
    if (callback && device) {
      callback(device);
    }
    if (onClose) onClose();
  };

  return (
    <div
      className="fixed z-50 min-w-[180px] bg-[--gray-9] dark:bg-[--gray-8] rounded-md overflow-hidden shadow-md"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="p-2 text-sm font-medium border-b border-[--gray-3] dark:border-[--gray-7] text-[--green-7] dark:text-[--green-6]">
        {device.name || "Device"}
      </div>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
        onClick={() => handleAction(onControl)}
      >
        <Settings className="mr-2 h-4 w-4" />
        {t("control")}
      </button>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
        onClick={() => handleAction(onEdit)}
      >
        <Edit className="mr-2 h-4 w-4" />
        {t("edit")}
      </button>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
        onClick={() => handleAction(onMove)}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="mr-2 h-4 w-4"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M5 9l-3 3 3 3"></path>
          <path d="M9 5l3-3 3 3"></path>
          <path d="M15 19l3 3 3-3"></path>
          <path d="M19 9l3 3-3 3"></path>
          <path d="M2 12h20"></path>
          <path d="M12 2v20"></path>
        </svg>
        {t("move")}
      </button>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
        onClick={() => handleAction(onDeactivate)}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="mr-2 h-4 w-4"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M18.36 6.64A9 9 0 0 1 20.77 15"></path>
          <path d="M6.16 6.16a9 9 0 1 0 12.68 12.68"></path>
          <path d="M12 2v4"></path>
          <path d="m2 2 20 20"></path>
        </svg>
        {t("deactivate")}
      </button>

      <div className="h-px bg-[--gray-3] dark:bg-[--gray-7]"></div>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--red-6] dark:text-[--red-5] transition-colors duration-150"
        onClick={() => handleAction(onDelete)}
      >
        <Trash className="mr-2 h-4 w-4" />
        {t("delete")}
      </button>
    </div>
  );
};
