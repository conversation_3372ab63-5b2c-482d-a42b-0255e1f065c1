import { useState, useCallback } from "react";
import type { Device } from "../services/deviceService";
import type { Target } from "../services/targetService";
import type { MapContextMenuData } from "../components/context-menu";

export const useMapContextMenus = () => {
  // Regular map context menu (for right-click on map)
  const [contextMenuData, setContextMenuData] =
    useState<MapContextMenuData | null>(null);

  // Device context menu
  const [deviceContextMenu, setDeviceContextMenu] = useState<{
    open: boolean;
    position: { x: number; y: number };
    device: Device | null;
  }>({
    open: false,
    position: { x: 0, y: 0 },
    device: null,
  });

  // Target context menu
  const [targetContextMenu, setTargetContextMenu] = useState<{
    open: boolean;
    position: { x: number; y: number };
    target: Target | null;
  }>({
    open: false,
    position: { x: 0, y: 0 },
    target: null,
  });

  // Handler for map context menu
  const handleMapContextMenu = useCallback((event: any) => {
    // Close any open menus first
    setDeviceContextMenu((prev) => ({ ...prev, open: false }));
    setTargetContextMenu((prev) => ({ ...prev, open: false }));

    if (event.detail.latLng) {
      event.domEvent?.preventDefault();
      const mouseEvent = event.domEvent as MouseEvent;

      setContextMenuData({
        coordinates: {
          lat: event.detail.latLng.lat,
          lng: event.detail.latLng.lng,
        },
        position: {
          x: mouseEvent.clientX,
          y: mouseEvent.clientY,
        },
      });
    }
  }, []);

  // Handler for closing all context menus
  const closeAllContextMenus = useCallback(() => {
    setContextMenuData(null);
    setDeviceContextMenu((prev) => ({ ...prev, open: false }));
    setTargetContextMenu((prev) => ({ ...prev, open: false }));
  }, []);

  // Handler for device marker context menu
  const openDeviceContextMenu = useCallback(
    (device: Device, position: { x: number; y: number }) => {
      setContextMenuData(null);
      setTargetContextMenu((prev) => ({ ...prev, open: false }));

      setDeviceContextMenu({
        open: true,
        position,
        device,
      });
    },
    []
  );

  // Handler for target marker context menu
  const openTargetContextMenu = useCallback(
    (target: Target, position: { x: number; y: number }) => {
      setContextMenuData(null);
      setDeviceContextMenu((prev) => ({ ...prev, open: false }));

      setTargetContextMenu({
        open: true,
        position,
        target,
      });
    },
    []
  );

  return {
    contextMenuData,
    deviceContextMenu,
    targetContextMenu,
    handleMapContextMenu,
    closeAllContextMenus,
    openDeviceContextMenu,
    openTargetContextMenu,
  };
};
