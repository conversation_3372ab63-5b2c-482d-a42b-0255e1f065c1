import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react";
import { useMapsLibrary } from "@vis.gl/react-google-maps";
import type { MapMouseEvent } from "@vis.gl/react-google-maps";
import throttle from "lodash/throttle";

// Custom Haversine formula to calculate distance between two points on Earth
// This will work even if the geometry library doesn't load
function calculateDistance(points: google.maps.LatLngLiteral[]): number {
  if (points.length < 2) return 0;

  let totalDistance = 0;

  for (let i = 0; i < points.length - 1; i++) {
    const p1 = points[i];
    const p2 = points[i + 1];
    totalDistance += haversineDistance(p1, p2);
  }

  return totalDistance;
}

function haversineDistance(
  p1: google.maps.LatLngLiteral,
  p2: google.maps.LatLngLiteral
): number {
  const toRadians = (degree: number) => (degree * Math.PI) / 180;

  const R = 6371e3; // Earth's radius in meters
  const φ1 = toRadians(p1.lat);
  const φ2 = toRadians(p2.lat);
  const Δφ = toRadians(p2.lat - p1.lat);
  const Δλ = toRadians(p2.lng - p1.lng);

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

export const useMapMeasuring = () => {
  const [open, setOpen] = useState(false);
  const [positions, setPositions] = useState<google.maps.LatLngLiteral[]>([]);
  const [distance, setDistance] = useState<number | undefined>(undefined);
  const isDraggingRef = useRef(false);

  // Make sure to load the geometry library
  const geometry = useMapsLibrary("geometry");

  // Use memoized callbacks for better performance
  const handleClose = useCallback(() => {
    setOpen(false);
    setPositions([]);
    setDistance(undefined);
  }, []);

  const handleOpen = useCallback(() => {
    setOpen(true);
  }, []);

  const handleMarkerDrag = useCallback(
    (ev: google.maps.MapMouseEvent, index: number) => {
      if (ev.latLng) {
        setPositions((prevState) => {
          return prevState.map((item, i) => {
            if (index === i) {
              return ev.latLng!.toJSON();
            }
            return item;
          });
        });
      }
    },
    []
  );

  const handleMarkerDragStart = useCallback(() => {
    isDraggingRef.current = true;
  }, []);

  const handleMarkerDragEnd = useCallback(() => {
    // Use a short timeout to avoid conflicts with click events
    setTimeout(() => {
      isDraggingRef.current = false;
    }, 300); // Reduced from 500ms for better responsiveness
  }, []);

  // Throttle marker creation more efficiently
  const handleCreateMarker = useMemo(() => {
    return throttle(
      (newLatLng?: google.maps.LatLngLiteral | null) => {
        if (isDraggingRef.current || !newLatLng) return;

        setPositions((prevPositions) => [...prevPositions, newLatLng]);
      },
      400, // Reduced from 600ms for better responsiveness
      { leading: true, trailing: false }
    );
  }, []);

  // Simplified map click handler
  const handleMapClick = useCallback(
    (ev: MapMouseEvent) => {
      if (!open || !ev.detail.latLng) return;
      handleCreateMarker(ev.detail.latLng);
    },
    [open, handleCreateMarker]
  );

  // Simplified polygon click handler
  const handlePolygonClick = useCallback(
    (ev: google.maps.MapMouseEvent) => {
      if (!open || !ev.latLng) return;
      handleCreateMarker(ev.latLng.toJSON());
    },
    [open, handleCreateMarker]
  );

  // Simplified marker click handler
  const handleMarkerClick = useCallback((index: number) => {
    setPositions((prevPositions) =>
      prevPositions.filter((_, i) => i !== index)
    );
  }, []);

  // Optimized distance calculation
  useEffect(() => {
    if (positions.length < 2) {
      if (positions.length > 0) {
        console.log(
          "Not enough positions to calculate distance:",
          positions.length
        );
      }
      setDistance(undefined);
      return;
    }

    try {
      let calculatedDistance;

      // Use Google Maps geometry if available, otherwise use our custom calculation
      if (geometry && geometry.spherical) {
        calculatedDistance = geometry.spherical.computeLength(positions);
      } else {
        calculatedDistance = calculateDistance(positions);
      }

      setDistance(calculatedDistance);
    } catch (error) {
      console.error("Error calculating distance:", error);

      // Fallback to custom calculation if there's an error
      try {
        const customDistance = calculateDistance(positions);

        setDistance(customDistance);
      } catch (err) {
        console.error("Error in fallback distance calculation:", err);
        setDistance(undefined);
      }
    }
  }, [geometry, positions]);

  // Return a memoized object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      open,
      positions,
      distance,
      onClose: handleClose,
      onOpen: handleOpen,
      onMarkerClick: handleMarkerClick,
      onMarkerDrag: handleMarkerDrag,
      onMarkerDragStart: handleMarkerDragStart,
      onMarkerDragEnd: handleMarkerDragEnd,
      onMapClick: handleMapClick,
      onPolygonClick: handlePolygonClick,
    }),
    [
      open,
      positions,
      distance,
      handleClose,
      handleOpen,
      handleMarkerClick,
      handleMarkerDrag,
      handleMarkerDragStart,
      handleMarkerDragEnd,
      handleMapClick,
      handlePolygonClick,
    ]
  );
};
