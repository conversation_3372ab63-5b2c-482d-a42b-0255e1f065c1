
from fastapi import APIRouter, HTTPException
from src.apps.auth.deps import UserDep
from src.apps.deps import ObjID_Dep
from src.apps.pro.utils.calculation import SweepCalculator
from src.conf.loader import get_motor_manager

router = APIRouter()

db = get_motor_manager()

collection_preset = db.pro.presets
collection_mod = db.pro.modulation

async def get_presets_db(org_id, _id: str | None = None):
    res = await collection_preset.find({"organization": org_id}).to_list()
    data = db.serialize(res)
    if _id:
        return [p for p in data if p["id"] == _id]
    return data


@router.get("/")
async def get_preset(user: UserDep, _id: str):
    if presets := await get_presets_db(user.organization, _id):
        return presets[0]
    raise HTTPException(status_code=404, detail="Preset not found")


@router.get("/list")
async def get_presets(user: UserDep):
    return await get_presets_db(user.organization)


@router.post("/", status_code=201)
async def create_preset(user: UserDep, data: dict) -> int:
    if _ := await collection_preset.find_one({"name": data["name"], "organization": user.organization}):
        raise HTTPException(
            status_code=400, detail="Preset with same name already exists"
        )
        
    if sweeps := data["sweeps"]:
        sweeps = SweepCalculator(
            start_frequency=data["start_frequency"],
            end_frequency=data["end_frequency"],
            sweep_params=sweeps["params"],
            sweep_time=sweeps["time"],
        )
    else:
        sweeps = SweepCalculator(
            start_frequency=data["start_frequency"],
            end_frequency=data["end_frequency"],
        )
    data["organization"] = user.organization
    data["sweeps"] = sweeps.get_sweeps_data()
    await collection_preset.insert_one(**data)


@router.put("/")
async def update_preset(_: UserDep, _id: ObjID_Dep, data: dict):
    collection_preset.update_one(_id, {"$set": data})



@router.delete("/", status_code=204)
async def delete_preset(_: UserDep, _id: ObjID_Dep):
    await collection_preset.delete_one(_id)



@router.get("/modulation/list")
async def get_modulation():
    res = await collection_mod.find().to_list()
    return db.serialize(res)


@router.get("/modulation/{name}")
async def get_preset_config(name: str, preset: str | None = None):
    res = await collection_mod.find_one({"name": name})
    data = db.serialize(res)
    return data


super_router = APIRouter(prefix="/super")
turbo_router = APIRouter(prefix="/turbo")

collection_super = db.pro.super_preset
collection_turbo = db.pro.turbo_preset

@super_router.get("/")
async def get_super_presets(_id: str):
    data = collection_super.find({"group": _id}).to_list()
    return db.serialize(data)


@super_router.post("/", status_code=201)
async def create_super_preset(data: dict):
    result = await collection_super.insert_one(data)
    return await db.get_by_id(collection_super, result.inserted_id)


@super_router.post("/by_name", status_code=201)
async def create_super_preset_by_name(preset_name: str, data: dict):
    preset = await collection_preset.find_one({"name": preset_name})
    if not preset:
        raise HTTPException(status_code=404, detail="Preset not found")
    data["_id"] = preset["id"]
    result = await collection_super.insert_one(data)
    return await db.get_by_id(collection_super, result.inserted_id)
    



@super_router.delete("/")
async def delete_super_preset(_id: ObjID_Dep):
    await collection_super.delete_one(_id)


@turbo_router.get("/")
async def get_turbo_presets(
    station_id: int | None = None,
    group_station_id: int | None = None,
) -> list:
    if not station_id and not group_station_id:
        raise HTTPException(
            status_code=400, detail="station_id or group_station_id is required"
        )
    if station_id:
        cond = {"station_id":  station_id}
    else:
        cond = {"_id":  group_station_id}
    data = await collection_turbo.find(cond).to_list()
    return db.serialize(data)



# @turbo_router.post("/", status_code=201)
# async def create_turbo_presets(
#     data: dict,
#     # name: str,
#     # station_id: int | None = None,
#     # group_station_id: int | None = None,
# ):
#     if not data["station_id"] and not data["group_station_id"]:
#         raise HTTPException(
#             status_code=400, detail="station_id or group_station_id is required"
#         )
#     await collection_turbo.insert_one()


@turbo_router.post("/", status_code=201)
async def create_turbo_presets(
    data: dict,
    name: str,
    station_id: int | None = None,
    group_station_id: int | None = None,
):
    if not station_id and not group_station_id:
        raise HTTPException(
            status_code=400, detail="station_id or group_station_id is required"
        )
    data2 = {"name": name, "station_id": station_id, "group_station_id": group_station_id}
    result = await collection_turbo.insert_one(data | data2)
    return await db.get_by_id(collection_turbo, result.inserted_id)


# @turbo_router.put("/", status_code=201)
# async def update_turbo_preset(data: sc.SuperPresetOut) -> int:
#     db_obj = tb.SuperPreset(**data.model_dump())
#     await db_obj.save()
#     return db_obj.id


# @turbo_router.delete("/", status_code=201)
# async def delete_turbo_preset(_id: str) -> int:
#     await tb.SuperPreset().delete().where(tb.SuperPreset.id == _id)


router.include_router(super_router)
router.include_router(turbo_router)
