from typing import Literal

from beanie import Document, Link
from pydantic import BaseModel

from . import ModulationDB


class Preset(BaseModel):
    name: str
    mod: str

    start_frequency: int
    end_frequency: int

    is_active: bool = True
    manage_option: Literal["auto", "manual"] = "auto"
    org: str | None = None


class PresetDB(Document, Preset):
    mod: Link[ModulationDB]

    class Settings:
        name = "pro.presets"
