from influxdb_client_3 import Point
from loguru import logger
from src.services.tsdb.client import TSDBClient


class PointsManager:

    def __init__(self, ts_db: TSDBClient) -> None:
        self.ts_db = ts_db
        self.prev_points: dict[str, Point] = {}
        self.new_points: dict[str, Point] = {}

    def write_unique_points(self) -> None:
        points_to_write = []
        for key, new_point in self.new_points.items():
            prev_point = self.prev_points.get(key)
            if not prev_point:
                p = Point.from_dict(new_point)
                points_to_write.append(p)
            elif prev_point["fields"] != new_point["fields"]:
                p = Point.from_dict(new_point)
                points_to_write.append(p)
            # self.prev_points[key] = new_point
        if points_to_write:
            self.ts_db.write(points_to_write)
        # self.prev_points = deepcopy(self.new_points)
        self.prev_points = self.new_points
        self.new_points = {}

    def _get_key_from_point_dict(self, point: dict):
        tags = "/".join(point["tags"].values())
        return f"{point['measurement']}/{tags}"

    def make_points(self, raspberry_id: str, content: dict):

        try:
            flor1 = int(float(content["temp"]["sensors_by_floor"]["1"]))
            flor2 = int(float(content["temp"]["sensors_by_floor"]["2"]))
            point = {
                "measurement": "temperature",
                "tags": {"raspberry_id": raspberry_id},
                "fields": {
                    "floor1": flor1,
                    "floor2": flor2,
                    "amplifier": content["temp"]["amplifier"],
                },
            }
            key = self._get_key_from_point_dict(point)
            self.new_points[key] = point

        except Exception as e:
            logger.error(e)

        try:
            for sdr_type, sdr in content["sdrs"].items():
                point = {
                    "measurement": "sdrs",
                    "tags": {"raspberry_id": raspberry_id, "sdr_type": sdr_type},
                    "fields": {
                        "status": sdr["status"],
                        "timer": sdr["timer"],
                    },
                }
                key = self._get_key_from_point_dict(point)
                self.new_points[key] = point
        except Exception as e:
            logger.error(e)

        try:
            for relay in content.get("relay", []):
                point = {
                    "measurement": "relay",
                    "tags": {
                        "raspberry_id": raspberry_id,
                        "led": relay["led"],
                    },
                    "fields": {
                        "status": relay["status"],
                    },
                }
                key = self._get_key_from_point_dict(point)
                self.new_points[key] = point
        except Exception as e:
            logger.error(e)
