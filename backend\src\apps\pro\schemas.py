from typing import Literal

from beanie import PydanticObjectId
from pydantic import BaseModel, Field


# It will generate schema automatically
class WithId(BaseModel):
    id: int


class FilterParams(BaseModel):
    limit: int = Field(100, gt=0, le=100)
    offset: int = Field(0, ge=0)
    ascending: bool = True


class StopMultipleSchema(BaseModel):
    device_ids: list[str]


class StartMultipleSchema(StopMultipleSchema):
    preset: str
    start_frequency: int
    end_frequency: int
    timer: int | None = 1800


class Add_Or_Remove_Relation(BaseModel):
    subject_id: PydanticObjectId
    object_id: PydanticObjectId
    action: Literal["add", "remove"] = "add"
