import copy
from asyncio import TaskGroup

from fastapi import APIRouter, BackgroundTasks, HTTPException, Request
from loguru import logger
from src.apps.auth.deps import AdminDep, UserDep
from src.apps.deps import MQTTDeps, ObjID_Dep, RedisDeps
from src.apps.pro.deps import <PERSON>Dep<PERSON>, <PERSON><PERSON>Dep<PERSON>, get_device
from src.apps.pro.schemas import StartMultipleSchema, StopMultipleSchema
from src.apps.pro.utils.calculation import SweepCalculator
from src.conf.loader import get_motor_manager

from ..models import StationDB

router = APIRouter()
db = get_motor_manager()

# async def _get_common_fields() -> tuple[List[str], List[str]]:
#     """Fetch common and all input field names from the database."""
#     fields = await tb.InputField.select(tb.InputField.name, tb.InputField.is_common)
#     all_fields = [field["name"] for field in fields]
#     common_fields = [field["name"] for field in fields if field["is_common"]]
#     return all_fields, common_fields


# async def build_start_data(data: Dict[str, Any]) -> Dict[str, Any]:
#     """Build structured data for SDR start request."""
#     all_fields, common_fields = await _get_common_fields()
#     signal_param = {}
#     common_param = {}

#     for key, value in data.items():
#         if key in all_fields:
#             if key in common_fields:
#                 common_param[key] = value
#             else:
#                 signal_param[key] = value

#     common_param["signal_param"] = signal_param
#     common_param["bw"] = data["end_frequency"] - data["start_frequency"]
#     common_param["range_start"] = data.get("start_frequency")
#     common_param["range_stop"] = data.get("end_frequency")
#     return common_param


@router.put("/edit/{station_id}")
async def edit_sdr_station(_id: ObjID_Dep, data: dict):
    await db.pro.stations.update_one(_id, {"$set": data})


@router.get("/list")
async def get_sdr_stations(user: UserDep):
    """Retrieve a list of SDR stations based on user role and organization."""
    q = {"organization": user.organization}
    stations = StationDB.find(fetch_links=True)

    return await stations.to_list()


@router.post("/stop")
async def sdr_stop(mqtt: MQTTDeps, device: DeviceDeps, with_resp: bool = False):
    """Stop an SDR device."""
    logger.debug(f"Stopping Device {device.id} Group {device.group.id}")
    payload = {"sdr_type": device.ampf_type.name}
    if with_resp:
        return await mqtt.publish_with_resp(
            "stop", payload, raspberry_id=device.group.raspberry_id
        )
    await mqtt.publish("stop", payload, raspberry_id=device.group.raspberry_id)


@router.post("/start")
async def sdr_start(
    user: UserDep,
    mqtt: MQTTDeps,
    mod_name: str,
    preset_name: str,
    device: DeviceDeps,
    data: dict,
    restart: bool = False,
    timer: int | None = 1800,
    with_resp: bool = False,
):
    print("starting")
    """Start an SDR device with the given configuration."""
    if restart:
        await sdr_stop(mqtt, device, with_resp=True)

    sweeps = SweepCalculator(**data)
    modulation = await db.pro.modulation.find_one({"name": mod_name})
    if not modulation:
        raise HTTPException(status_code=404, detail="Modulation not found")

    # event = tb.DeviceEvents(device=device, user=user)
    # await event.save()

    data.update(
        {
            "event_id": 1,
            "device_id": str(device.id),
            "sweep_params": sweeps.get_sweeps_data(),
            "signal_param": {
                **data["signal_param"],
                "signal_type": modulation.rasp_key,
                "mod_name": mod_name,
            },
            "sdr_type": device.ampf_type.name,
            "preset_name": preset_name,
            "id": device.id,
            "timer": timer or 1800,
        }
    )

    mqtt.publish("start", data, raspberry_id=device.group.raspberry_id)


@router.post("/start_multiple_sdr")
async def start_multiple_sdr(user: UserDep, mqtt: MQTTDeps, data: StartMultipleSchema):
    """Start multiple SDR devices with a shared preset."""
    preset = await db.pro.presets.find_one({"name": data.preset})
    if not preset:
        raise HTTPException(status_code=404, detail="Preset not found")

    preset.update(
        {
            "sweep_params": preset["sweeps"]["params"],
            "sweep_time": preset["sweeps"]["time"],
            "start_frequency": data.start_frequency,
            "end_frequency": data.end_frequency,
        }
    )

    devices = await db.pro.devices.find({"group": data.device_ids}).to_list()

    async with TaskGroup() as tg:
        for device in devices:
            tg.create_task(
                sdr_start(
                    user,
                    mqtt,
                    preset["mod_name"],
                    preset["name"],
                    device,
                    copy.deepcopy(preset),
                    timer=data.timer,
                    with_resp=False,
                )
            )


@router.post("/super_preset/start")
async def start_super_preset(user: UserDep, mqtt: MQTTDeps, _id: str):
    """Start an SDR device using a super preset configuration."""
    super_preset = await db.get_by_id(db.pro.super_presets, _id)

    sweeps = super_preset["preset"].pop("sweeps")
    sweep_data = {
        "sweep_params": sweeps["params"],
        "sweep_time": sweeps["time"],
    }
    preset_dict = super_preset["preset"] | sweep_data

    device = await db.pro.devices.find_one({"id": super_preset["device_id"]})
    await sdr_start(
        user,
        mqtt,
        super_preset.modulation.name,
        super_preset.preset.name,
        device,
        preset_dict,
        timer=super_preset.preset.duration,
        with_resp=False,
    )


@router.post("/super_preset/stop")
async def stop_super_preset(mqtt: MQTTDeps, _id: str):
    """Stop an SDR device associated with a super preset."""
    super_preset = await db.get_by_id(db.pro.super_presets, _id)

    device = await db.pro.devices.find_one({"id": super_preset["device_id"]})

    logger.debug(f"Stopping Device {device}")
    mqtt.publish(
        "stop", {"sdr_type": device.sdr_type.name}, raspberry_id=device.raspberry_id
    )


@router.post("/restart")
async def sdr_restart(
    user: UserDep,
    mqtt: MQTTDeps,
    mod_name: str,
    device: DeviceDeps,
    data: dict,
    timer: int | None = 360,
    with_resp: bool = False,
):
    """Restart an SDR device with new configuration."""
    logger.debug(f"Restarting SDR {device.id} Group {device.group.id}")
    await sdr_stop(mqtt, device, with_resp=True)
    await sdr_start(
        user,
        mqtt=mqtt,
        mod_name=mod_name,
        preset_name="restart_preset",
        device=device,
        data=data,
        timer=timer,
        with_resp=with_resp,
    )


@router.post("/stop_all")
async def sdr_stop_all(controller: ControllerDeps, station_id: int):
    """Stop all SDR devices for a given station."""
    await controller.stop_devices_for_station(station_id)
    return {"message": "All devices stopped successfully"}


@router.post("/stop_multiple_sdr")
async def stop_multiple_sdr(mqtt: MQTTDeps, data: StopMultipleSchema):
    """Stop multiple SDR devices."""
    async with TaskGroup() as tg:
        for device_id in data.device_ids:
            device = await get_device(device_id)
            tg.create_task(sdr_stop(mqtt, device))


@router.get("/devices/list/")
async def get_groups_with_devices(station_id: str):
    """Retrieve device groups with associated devices for a station."""
    return  # TODO rework this query
    return await db_utils.get_groups_with_devices(station_id)


@router.get("/devices/list/full")
async def get_full_devices():
    """Retrieve all device groups with devices across all stations."""
    stations = await db.pro.stations.find().to_list()
    data = []
    for station in stations:
        groups = await get_groups_with_devices(station["id"])
        data.extend(groups)
    return data


@router.post("/switch_relay")
async def switch_relay(mqtt: MQTTDeps, raspberry_id: str, led: int = 0):
    """Switch relay for a device group."""
    mqtt.publish("switch_relay", {"led": led}, raspberry_id=raspberry_id)


@router.get("/state")
async def get_raspberry_state(redis: RedisDeps, raspberry_id: str):
    """Retrieve the state of a device group by raspberry ID."""
    if group_data := redis.group.get_data(raspberry_id):
        return group_data
    raise HTTPException(status_code=404, detail="Device group not found")


@router.get("/state/{station_id}")
async def get_station_state(redis: RedisDeps, station_id: int):
    """Retrieve the state of all devices in a station."""
    return redis.get_all()


@router.get("/state/ecoflow/{ecoflow_id}")
async def get_ecoflow_state(redis: RedisDeps, ecoflow_id: str):
    """Retrieve the state of an Ecoflow device."""
    return redis.ecoflow.get_data(ecoflow_id)


@router.get("/reload_ecoflow")
async def reload_ecoflow(ecoflow_id: int, r: Request, bg_tasks: BackgroundTasks):
    """Reload Ecoflow device state in the background."""
    bg_tasks.add_task(r.app.state.ecoflow.reload)
    return {"message": "Ecoflow reload task started"}


@router.patch("/add_access_to_station")
async def add_access_to_station(_: AdminDep, station_id: str, username: str):
    """Grant a user access to a specific station."""
    station = await db.get_by_id_or_err(db.pro.stations, station_id)
    user = await db.users.find_one({"username": username})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Update the user's stations array

    await db.users.update_one(
        {"username": username},
        {
            "$addToSet": {"stations": station._id}
        },  # Using $addToSet to prevent duplicates
    )
