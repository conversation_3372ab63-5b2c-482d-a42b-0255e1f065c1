import psycopg2
from pymongo import MongoClient

# PostgreSQL connection details
pg_conn_params = {
    "dbname": "asap",
    "user": "postgres",
    "password": "postgres",
    "host": "localhost",
    "port": "5432",
}

# MongoDB connection details
mongo_uri = "mongodb://mongo-admin:password123!.@localhost:27017"
mongo_db_name = "test"
mongo_collection_name = "users"

try:
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    # Connect to MongoDB
    mongo_client = MongoClient(mongo_uri)
    mongo_db = mongo_client[mongo_db_name]
    mongo_collection = mongo_db[mongo_collection_name]

    # Fetch users from PostgreSQL (only username and role)
    pg_cursor.execute(
        "SELECT username, email, role FROM piccolo_user JOIN profile ON piccolo_user.id = profile.user"
    )

    users = pg_cursor.fetchall()

    unique_emails = []

    # Prepare data for MongoDB insertion
    mongo_docs = []
    for user in users:
        username, email, role = user
        if email not in unique_emails:
            mongo_docs.append({"username": username, "role": role, "email": email})
            print(f"Added user {username} with role {role}, email {email}")
        else:
            unique_emails.append(email)

    # Insert into MongoDB
    if mongo_docs:
        mongo_collection.insert_many(mongo_docs)
        print(f"Successfully inserted {len(mongo_docs)} users into MongoDB.")
    else:
        print("No users found to migrate.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    # Close PostgreSQL connection
    if pg_cursor:
        pg_cursor.close()
    if pg_conn:
        pg_conn.close()

    # Close MongoDB connection
    if mongo_client:
        mongo_client.close()
