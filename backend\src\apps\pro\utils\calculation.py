import math


def _skip_blocked_range(
    start: int, stop: int, blocks: list[tuple[int, int]]
) -> list[tuple[int, int]]:
    """
    Filters a range to exclude portions within blocked ranges.

    Args:
        start: The start of the range.
        stop: The end of the range.
        blocks: A list of tuples, where each tuple represents a blocked range (start, end).

    Returns:
        A list of tuples, where each tuple represents a non-blocked portion of the original range.
        Returns an empty list if the entire range is blocked.
    """

    result = []
    current_start = start
    current_stop = stop

    for block_start, block_end in sorted(
        blocks
    ):  # Sort blocks to handle overlaps correctly
        if current_start >= current_stop:  # no more range to check
            break

        if current_start >= block_end:  # Block is before the current range
            continue

        if current_stop <= block_start:  # Block is after the current range
            continue

        if current_start < block_start:  # Part of the range is before the block
            result.append(
                (current_start, min(current_stop, block_start))
            )  # min to avoid over extending the range

        if current_stop > block_end:  # Part of the range is after the block
            current_start = block_end  # update the start for the next iteration
            continue  # no need to update current_stop, since it will be updated on the next iteration or will be the same
        else:  # the whole range is within the block
            current_start = current_stop  # no more range to check
            break

    if current_start < current_stop:  # add the remaining part if any
        result.append((current_start, current_stop))

    return result


"""
# Example usage:
ranges = [(100, 115), (120, 140)]
blocks = [(110, 150), (200, 300)]

filtered_ranges = []
for r_start, r_stop in ranges:
    parts = _skip_blocked_range(r_start, r_stop, blocks)
    filtered_ranges.extend(parts)

print(filtered_ranges)  # Output: [(100, 110)]

ranges = [(100, 150)]
blocks = [(110, 140)]

filtered_ranges = []
for r_start, r_stop in ranges:
    parts = _skip_blocked_range(r_start, r_stop, blocks)
    filtered_ranges.extend(parts)

print(filtered_ranges)  # Output: [(100, 110), (140, 150)]

ranges = [(100, 200)]
blocks = [(110, 140), (150, 180)]

filtered_ranges = []
for r_start, r_stop in ranges:
    parts = _skip_blocked_range(r_start, r_stop, blocks)
    filtered_ranges.extend(parts)

print(filtered_ranges)  # Output: [(100, 110), (140, 150), (180, 200)]


ranges = [(100, 120), (120, 140)]
blocks = [(100, 150)]

filtered_ranges = []
for r_start, r_stop in ranges:
    parts = _skip_blocked_range(r_start, r_stop, blocks)
    filtered_ranges.extend(parts)

print(filtered_ranges)  # Output: []

ranges = [(100, 120), (120, 140)]
blocks = [(150, 200)]

filtered_ranges = []
for r_start, r_stop in ranges:
    parts = _skip_blocked_range(r_start, r_stop, blocks)
    filtered_ranges.extend(parts)

print(filtered_ranges)  # Output: [(100, 120), (120, 140)]
"""


type blocks_type = list[tuple[int, int]] | None
type sweeps_type = list[tuple[int, int]]


class SweepCalculator:

    def __init__(
        self,
        start_frequency: int,
        end_frequency: int,
        bw: int | None = None,
        sweep_time=100,
        sweep_params: list[tuple[int, int]] | None = None,
        # blocks: blocks_type = None,
        **kwargs,
    ):
        self._sweeps = []
        self.time = sweep_time
        if sweep_params:
            self._sweeps = sweep_params
        else:
            # Calculate sweeps automatically if sweep_params is not provided
            bw = bw or kwargs.get("bandwidth", 56)
            self.start_frequency = start_frequency
            self.end_frequency = end_frequency
            self.width = end_frequency - start_frequency
            self.sweep_number = math.ceil(self.width / bw)
            self.bw = math.ceil(self.width / self.sweep_number)
            self._calculate_sweep()

    def _calculate_sweep(self):
        current_start = self.start_frequency
        for i in range(self.sweep_number):
            current_end = current_start + self.bw
            if current_end > self.end_frequency:
                current_end = self.end_frequency
            self._sweeps.append([current_start, current_end])
            current_start = current_end

    def get_sweeps_data(self):
        if self._sweeps:
            return {"params": self.get(), "time": self.time}

    def get(self) -> sweeps_type:
        return self._sweeps
