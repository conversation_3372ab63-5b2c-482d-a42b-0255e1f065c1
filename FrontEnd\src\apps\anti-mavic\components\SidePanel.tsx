import { Link } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { ArrowLeft, Circle, RotateCw } from "lucide-react";
import MiniCompass from "./controls/MiniCompass";

const SidePanel = () => {
  const { t } = useTranslation("antimavic");

  // Fuel level data
  const fuelLevel = "78%";
  const instantaneousConsumption = "2.6 l/hr";
  const estimatedTimeRemaining = "1h 25 min";

  return (
    <div className="w-80 min-w-[280px] max-w-[400px] bg-[--gray-9] text-[--gray-1] flex flex-col h-full shadow-xl">
      {/* Header with back button */}
      <div className="p-4 border-b border-[--gray-8] flex items-center flex-shrink-0">
        <Link
          to="/"
          className="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-[--gray-8] transition-colors"
          aria-label={t("app.back")}
          tabIndex={0}
        >
          <ArrowLeft size={18} />
        </Link>
        <span className="ml-2 font-medium">{t("app.back")}</span>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col p-4 space-y-4 min-h-0">
        {/* Start Recording Button */}
        <div className="text-right">
          <button className="bg-transparent border border-[--gray-6] hover:bg-[--gray-8] text-[--gray-1] px-4 py-2 rounded-lg text-sm flex items-center gap-2 ml-auto transition-colors">
            <Circle size={16} className="text-red-500" />
            {t("controls.actions.startRecording", "Start Recording")}
          </button>
        </div>

        {/* Fuel Level */}
        <div className="bg-[--gray-8] rounded-lg p-3 text-sm">
          <div className="flex items-center justify-between mb-2">
            <span>{t("controls.fuel.level", "Fuel level %")}</span>
            <div className="text-xs text-[--gray-4] bg-[--gray-7] px-2 py-1 rounded">
              ⓘ
            </div>
          </div>

          <div className="flex items-center justify-between mb-3">
            <span className="text-2xl font-bold text-[--green-5]">
              {fuelLevel}
            </span>
            <span className="text-sm text-[--gray-4]">remaining</span>
          </div>

          <div className="w-full bg-[--gray-7] rounded-full h-2 mb-3">
            <div
              className="bg-[--green-5] h-2 rounded-full transition-all duration-300"
              style={{ width: "78%" }}
            ></div>
          </div>

          <div className="text-xs text-[--gray-4] space-y-1">
            <div>Instantaneous consumption: {instantaneousConsumption}</div>
            <div>Estimated time remaining: {estimatedTimeRemaining}</div>
          </div>
        </div>

        {/* Turn To (Rotate Input) */}
        <div className="bg-[--gray-8] rounded-lg p-3 text-sm">
          <div className="mb-2">
            <span>{t("controls.rotation.turnTo", "Turn To")}</span>
          </div>
          <div className="flex items-center gap-2">
            <input
              type="text"
              defaultValue="00"
              className="bg-[--gray-7] text-white px-3 py-1 rounded text-sm flex-1 border-none outline-none focus:ring-1 focus:ring-[--green-5]"
              placeholder="00"
            />
            <button className="bg-[--gray-7] hover:bg-[--gray-6] text-white px-3 py-1 rounded text-sm transition-colors">
              {t("controls.rotation.rotate", "Rotate")}
            </button>
          </div>
        </div>

        {/* Spacer to push compass to bottom */}
        <div className="flex-1 min-h-0"></div>

        {/* Compass at bottom */}
        <div className="flex justify-center mt-auto">
          <div className="bg-[--gray-8] rounded-lg p-3 w-32 h-32 flex items-center justify-center">
            <MiniCompass />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SidePanel;
