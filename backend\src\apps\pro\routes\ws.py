import asyncio

from fastapi import APIRouter, WebSocket
from src.services.redis import RedisManager

router = APIRouter()


redis = RedisManager()


@router.websocket("/station")
async def websocket_endpoint(websocket: WebSocket):
    print("Connected to websocket", websocket)
    await websocket.accept()
    while True:
        await asyncio.sleep(1)
        data = redis.get_all()
        try:
            await websocket.send_json(data)
        except Exception:
            pass
