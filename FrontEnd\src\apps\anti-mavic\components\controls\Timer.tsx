import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Timer as TimerIcon } from "lucide-react";

interface TimerProps {
  timerValue: string;
  timerTarget: string;
}

const Timer = ({ timerValue, timerTarget }: TimerProps) => {
  const { t } = useTranslation("antimavic");
  const [isEditing, setIsEditing] = useState(false);

  const handleEdit = () => {
    setIsEditing(!isEditing);
  };

  return (
    <div>
      <div className="flex items-center gap-1 mb-2">
        <TimerIcon size={16} className="text-[--green-5]" />
        <h4 className="font-medium text-sm">
          {t("controls.timer.title")}
        </h4>
      </div>
      
      <div className="text-center text-xl font-bold">
        {timerValue}
      </div>
      
      <div className="text-center text-[10px] text-[--gray-4] mt-1">
        {t("controls.timer.setTo", { target: timerTarget })}
      </div>
      
      <div className="flex justify-end mt-2">
        <button 
          className="text-xs text-[--green-5] hover:underline"
          onClick={handleEdit}
        >
          {t("controls.timer.edit")}
        </button>
      </div>
    </div>
  );
};

export default Timer;
