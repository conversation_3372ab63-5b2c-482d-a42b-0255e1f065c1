import { useTranslation } from "react-i18next";
import {
  BatteryMedium,
  Thermometer,
  Zap,
  Settings as SettingsIcon,
} from "lucide-react";
import Map from "./controls/Map";
import type { AspectRatioData } from "../utils/useVideoAspectRatio";

interface BottomPanelProps {
  aspectRatioData: AspectRatioData;
}

const BottomPanel = ({ aspectRatioData }: BottomPanelProps) => {
  const { t } = useTranslation("antimavic");

  // Battery and system stats (hardcoded for now)
  const batteryLevel = "78%";
  const workTime = "4h 35 min";
  const voltage = "12.6 V";
  const current = "3.2 A";
  const power = "40.3W";
  const temperature = "83°";

  return (
    <div
      className="bg-[--gray-9] text-[--gray-1] p-4 border-t border-[--gray-8] flex-shrink-0 transition-all duration-300 ease-in-out"
      style={{
        height: `${aspectRatioData.bottomPanelHeight}px`,
        minHeight: `${Math.max(160, aspectRatioData.bottomPanelHeight * 0.8)}px`,
        maxHeight: `${Math.min(300, aspectRatioData.bottomPanelHeight * 1.2)}px`,
      }}
    >
      <div className="flex gap-4 h-full">
        {/* Left side - Map */}
        <div className="w-80 min-w-[280px] h-full rounded-lg overflow-hidden bg-[--gray-8]">
          <Map />
        </div>

        {/* Stats and controls area - 3 columns only */}
        <div className="flex-1 grid grid-cols-3 gap-4 min-w-0">
          {/* Column 1: Battery + Current Consumption */}
          <div className="flex flex-col gap-2 min-w-0">
            {/* Battery Level */}
            <div className="bg-[--gray-8] rounded-lg p-2 text-xs flex-1 min-h-0">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-1">
                  <BatteryMedium size={14} className="text-[--green-5]" />
                  <span>{t("controls.battery.level")}</span>
                </div>
                <span className="font-medium text-sm">{batteryLevel}</span>
              </div>
              <div className="text-xs text-[--gray-4] mb-2">
                {t("controls.battery.remaining")}
              </div>
              <div className="w-full bg-[--gray-7] rounded-full h-1.5">
                <div
                  className="bg-[--green-5] h-1.5 rounded-full transition-all duration-300"
                  style={{ width: "78%" }}
                ></div>
              </div>
              <div className="text-xs text-[--gray-4] mt-1">
                {t("controls.battery.estimatedTime")}: {workTime}
              </div>
            </div>

            {/* Current Consumption */}
            <div className="bg-[--gray-8] rounded-lg p-2 text-xs flex-1 min-h-0">
              <div className="flex items-center gap-1 mb-2">
                <Zap size={14} className="text-[--green-5]" />
                <span>{t("controls.consumption.title")}</span>
              </div>
              <div className="grid grid-cols-3 gap-1 text-xs">
                <div className="text-center">
                  <div className="text-[--gray-4]">
                    {t("controls.consumption.voltage")}
                  </div>
                  <div className="font-medium text-[--green-5]">{voltage}</div>
                </div>
                <div className="text-center">
                  <div className="text-[--gray-4]">
                    {t("controls.consumption.current")}
                  </div>
                  <div className="font-medium text-[--green-5]">{current}</div>
                </div>
                <div className="text-center">
                  <div className="text-[--gray-4]">
                    {t("controls.consumption.power")}
                  </div>
                  <div className="font-medium text-[--green-5]">{power}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Column 2: Settings + Temperature */}
          <div className="flex flex-col gap-2 min-w-0">
            {/* Settings */}
            <div className="bg-[--gray-8] rounded-lg p-2 text-xs flex-1 min-h-0">
              <div className="flex items-center gap-1 mb-2">
                <SettingsIcon size={14} className="text-[--green-5]" />
                <span>{t("controls.settings.title")}</span>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-[--gray-4]">Powder Saving Mode</span>
                  <div className="w-8 h-4 bg-[--gray-7] rounded-full relative">
                    <div className="w-3 h-3 bg-white rounded-full absolute top-0.5 right-0.5"></div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-[--gray-4]">Auto Cooling</span>
                  <div className="w-8 h-4 bg-[--green-5] rounded-full relative">
                    <div className="w-3 h-3 bg-white rounded-full absolute top-0.5 right-0.5"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Temperature */}
            <div className="bg-[--gray-8] rounded-lg p-2 text-xs flex-1 min-h-0">
              <div className="flex items-center gap-1 mb-2">
                <Thermometer size={14} className="text-[--green-5]" />
                <span>{t("controls.temperature.title")}</span>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-[--green-5] mb-1">
                  {temperature}
                </div>
                <div className="text-xs text-[--gray-4] mb-2">
                  {t("controls.temperature.normal")}
                </div>
                <div className="w-full bg-[--gray-7] rounded-full h-1.5">
                  <div
                    className="bg-[--green-5] h-1.5 rounded-full transition-all duration-300"
                    style={{ width: "60%" }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* Column 3: Stop Device + Timer */}
          <div className="flex flex-col gap-2 min-w-0">
            {/* Stop Device Button */}
            <div className="flex-1 flex items-center justify-center min-h-0">
              <button className="bg-[--green-5] hover:bg-[--green-6] text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors w-full">
                ⏹ {t("controls.actions.stopDevice", "Stop Device")}
              </button>
            </div>

            {/* Timer */}
            <div className="bg-[--gray-8] rounded-lg p-2 text-xs flex-1 min-h-0">
              <div className="flex items-center gap-1 mb-2">
                <div className="w-2 h-2 bg-[--green-5] rounded-full"></div>
                <span>{t("controls.timer.title")}</span>
                <span className="text-[--green-5] text-xs">Active</span>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-white mb-1">
                  00:01:42
                </div>
                <div className="text-xs text-[--gray-4]">Set to Complete</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BottomPanel;
