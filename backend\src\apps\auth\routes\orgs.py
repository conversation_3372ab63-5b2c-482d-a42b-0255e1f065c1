from fastapi import APIRouter, HTTPException, Response
from src.apps.auth.deps import AdminDep
from src.apps.deps import ObjID_Dep
from src.conf.loader import get_motor_manager

from ..models import Org, OrgDB

router = APIRouter()

db = get_motor_manager()

router = APIRouter()


@router.get("/")
async def get_all_orgs():
    return await OrgDB.find().to_list()


@router.get("/{_id}")
async def get_one(_id: ObjID_Dep):
    return await OrgDB.find_one(_id)


@router.post("/", response_model=OrgDB)
async def create_org(new_org: Org):
    org = OrgDB(**new_org.model_dump())
    await org.create()
    return org


@router.patch("/{_id}")
async def update_org(_id: ObjID_Dep, new_org: dict):
    org = await OrgDB.find_one(_id)
    if not org:
        raise HTTPException(404, "org not found")

    org = org.model_copy(update=new_org)
    await org.save()


@router.delete("/")
async def delete_org(_: AdminDep, _id: ObjID_Dep) -> Response:
    await OrgDB.find_one(_id).delete()
    return Response(status_code=204)
