import { useTranslation } from "react-i18next";
import * as Switch from "@radix-ui/react-switch";
import { MapPin } from "lucide-react";

interface ConflictLinesToggleProps {
  isVisible: boolean;
  onToggle: (visible: boolean) => void;
}

export const ConflictLinesToggle = ({ isVisible, onToggle }: ConflictLinesToggleProps) => {
  const { t } = useTranslation("map");

  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-[--military-light] rounded-lg">
      <div className="flex items-center gap-3">
        <MapPin className="h-4 w-4 text-gray-700 dark:text-[--khaki-light]" />
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-700 dark:text-[--sand-light]">
            {t("conflict_lines")}
          </span>
          <span className="text-xs text-gray-500 dark:text-[--khaki-medium]">
            {t("show_conflict_lines_description")}
          </span>
        </div>
      </div>
      <Switch.Root
        checked={isVisible}
        onCheckedChange={onToggle}
        className="relative h-[24px] w-[44px] cursor-pointer rounded-full bg-gray-200 shadow-sm data-[state=checked]:bg-[--green-6] dark:bg-[--military-medium] dark:border dark:border-[--military-accent] border border-gray-300 transition-colors duration-200"
      >
        <Switch.Thumb className="block h-[20px] w-[20px] translate-x-[2px] rounded-full bg-white shadow-sm transition-transform duration-200 will-change-transform data-[state=checked]:translate-x-[22px] dark:bg-[--sand-light] dark:border dark:border-[--military-light] border border-gray-200" />
      </Switch.Root>
    </div>
  );
}; 