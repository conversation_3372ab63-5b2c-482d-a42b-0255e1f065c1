import { useState, useCallback, useRef, useEffect } from 'react';

interface UseResizableColumnsProps {
  initialWidths?: number[];
  minWidth?: number;
}

export const useResizableColumns = ({ 
  initialWidths = [40, 30, 30], // percentages
  minWidth = 15 
}: UseResizableColumnsProps = {}) => {
  const [columnWidths, setColumnWidths] = useState(initialWidths);
  const [isResizing, setIsResizing] = useState(false);
  const [resizingColumn, setResizingColumn] = useState<number | null>(null);
  const startX = useRef(0);
  const startWidths = useRef<number[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent, columnIndex: number) => {
    setIsResizing(true);
    setResizingColumn(columnIndex);
    startX.current = e.clientX;
    startWidths.current = [...columnWidths];
    e.preventDefault();
  }, [columnWidths]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing || resizingColumn === null || !containerRef.current) return;

    const containerWidth = containerRef.current.offsetWidth;
    const deltaX = e.clientX - startX.current;
    const deltaPercent = (deltaX / containerWidth) * 100;

    const newWidths = [...startWidths.current];
    const currentWidth = newWidths[resizingColumn];
    const nextWidth = newWidths[resizingColumn + 1];

    // Calculate new widths
    const newCurrentWidth = Math.max(minWidth, currentWidth + deltaPercent);
    const newNextWidth = Math.max(minWidth, nextWidth - deltaPercent);

    // Only update if both columns can maintain minimum width
    if (newCurrentWidth >= minWidth && newNextWidth >= minWidth) {
      newWidths[resizingColumn] = newCurrentWidth;
      newWidths[resizingColumn + 1] = newNextWidth;
      setColumnWidths(newWidths);
    }
  }, [isResizing, resizingColumn, minWidth]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    setResizingColumn(null);
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const getColumnStyle = (index: number) => ({
    width: `${columnWidths[index]}%`,
  });

  return {
    columnWidths,
    isResizing,
    resizingColumn,
    containerRef,
    handleMouseDown,
    getColumnStyle,
  };
}; 