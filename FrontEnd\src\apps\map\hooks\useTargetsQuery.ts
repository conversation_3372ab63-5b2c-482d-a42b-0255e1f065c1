import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { targetService } from "../services/targetService";
import type { Target } from "../services/targetService";

export const TARGETS_QUERY_KEY = "targets";

export function useTargetsQuery() {
  return useQuery({
    queryKey: [TARGETS_QUERY_KEY],
    queryFn: () => targetService.getAllTargets(),
  });
}

export function useCreateTargetMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (target: Omit<Target, "id">) =>
      targetService.createTarget(target),
    onSuccess: (newTarget) => {
      queryClient.setQueryData<Target[]>(
        [TARGETS_QUERY_KEY],
        (oldTargets = []) => [...oldTargets, newTarget as Target]
      );
    },
  });
}

export function useUpdateTargetMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, target }: { id: string; target: Partial<Target> }) =>
      targetService.updateTarget(id, target),
    onMutate: async ({ id, target }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: [TARGETS_QUERY_KEY] });

      // Snapshot the previous value
      const previousTargets = queryClient.getQueryData<Target[]>([
        TARGETS_QUERY_KEY,
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData<Target[]>(
        [TARGETS_QUERY_KEY],
        (oldTargets = []) => {
          return oldTargets.map((t) => {
            if (t._id === id) {
              return { ...t, ...target, _id: id };
            }
            return t;
          });
        }
      );

      return { previousTargets };
    },
    onError: (err, { id }, context) => {
      // If there was an error, roll back to the previous value
      if (context?.previousTargets) {
        queryClient.setQueryData([TARGETS_QUERY_KEY], context.previousTargets);
      }
    },
    onSettled: () => {
      // Refetch after error or success
      queryClient.invalidateQueries({ queryKey: [TARGETS_QUERY_KEY] });
    },
  });
}

export function useDeleteTargetMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => targetService.deleteTarget(id),
    onSuccess: (_, id) => {
      queryClient.setQueryData<Target[]>(
        [TARGETS_QUERY_KEY],
        (oldTargets = []) => oldTargets.filter((t) => t._id !== id)
      );
    },
  });
}
