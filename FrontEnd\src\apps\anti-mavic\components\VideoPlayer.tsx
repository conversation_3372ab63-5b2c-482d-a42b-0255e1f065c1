import { useRef, useEffect, forwardRef, useImperativeHandle, useState } from "react";

interface VideoPlayerProps {
  src: string;
  title: string;
  isActive: boolean;
  isLoading: boolean;
  onLoadedData: () => void;
  onError: () => void;
  className?: string;
}

export interface VideoPlayerRef {
  play: () => void;
  pause: () => void;
  getCurrentTime: () => number;
  setCurrentTime: (time: number) => void;
}

const VideoPlayer = forwardRef<VideoPlayerRef, VideoPlayerProps>(
  ({ src, title, isActive, isLoading, onLoadedData, onError, className = "" }, ref) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [hasError, setHasError] = useState(false);
    const [videoLoaded, setVideoLoaded] = useState(false);

    useImperativeHandle(ref, () => ({
      play: () => {
        if (videoRef.current) {
          videoRef.current.play().catch(console.error);
        }
      },
      pause: () => {
        if (videoRef.current) {
          videoRef.current.pause();
        }
      },
      getCurrentTime: () => {
        return videoRef.current?.currentTime || 0;
      },
      setCurrentTime: (time: number) => {
        if (videoRef.current) {
          videoRef.current.currentTime = time;
        }
      },
    }));

    useEffect(() => {
      if (isActive && videoRef.current && !isLoading && videoLoaded) {
        videoRef.current.play().catch(console.error);
      } else if (!isActive && videoRef.current) {
        videoRef.current.pause();
      }
    }, [isActive, isLoading, videoLoaded]);

    const handleVideoLoad = () => {
      console.log(`✅ Video loaded successfully: ${src}`);
      setVideoLoaded(true);
      setHasError(false);
      onLoadedData();
    };

    const handleVideoError = (e: any) => {
      console.error(`❌ Error loading video: ${src}`, e);
      setHasError(true);
      setVideoLoaded(false);
      onError();
    };

    return (
      <div className={`relative w-full h-full ${className}`}>
        <video
          ref={videoRef}
          src={src}
          className="w-full h-full object-cover"
          title={title}
          loop
          muted
          playsInline
          preload="metadata"
          onLoadedData={handleVideoLoad}
          onError={handleVideoError}
          style={{
            opacity: isLoading || hasError ? 0 : 1,
            transition: "opacity 0.2s ease",
          }}
        />
        
        {/* Video info overlay (visible when video fails to load OR when no video is loaded yet) */}
        {(hasError || (!videoLoaded && !isLoading)) && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-800 text-white text-sm z-20">
            <div className="text-center">
              <div className="mb-2 text-2xl">
                {hasError ? "❌" : "📹"}
              </div>
              <div className="font-medium">
                {hasError ? `Error loading ${title}` : `Loading ${title}...`}
              </div>
              <div className="text-xs text-gray-400 mt-2 max-w-xs">
                {hasError ? "Video file should be at:" : "Trying to load video from:"}
              </div>
              <div className="text-xs text-blue-400 mt-1 font-mono">
                {src.split('?')[0]} {/* Remove timestamp from display */}
              </div>
              {hasError && (
                <div className="text-xs text-gray-500 mt-2">
                  Supported formats: MP4, WebM, OGV
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);

VideoPlayer.displayName = "VideoPlayer";

export default VideoPlayer; 