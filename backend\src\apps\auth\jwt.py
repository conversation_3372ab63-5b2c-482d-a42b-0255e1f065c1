"""FastAPI JWT configuration."""

from datetime import timedelta

from fastapi_jwt import JwtAccess<PERSON>earer, JwtAuthorizationCredentials

from .models.user import UserDB

ACCESS_EXPIRES = timedelta(hours=24)
REFRESH_EXPIRES = timedelta(days=30)

access_security = JwtAccessBearer(
    "CONFIG.authjwt_secret_key",
    access_expires_delta=ACCESS_EXPIRES,
    refresh_expires_delta=REFRESH_EXPIRES,
)


async def user_from_credentials(auth: JwtAuthorizationCredentials) -> UserDB | None:
    """Return the user associated with auth credentials."""
    return await UserDB.by_username(auth.subject["username"])


async def user_from_token(token: str) -> UserDB | None:
    """Return the user associated with a token value."""
    payload = access_security._decode(token)
    return await UserDB.by_username(payload["subject"]["username"])
