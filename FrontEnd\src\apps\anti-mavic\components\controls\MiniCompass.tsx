import React from "react";
import { useTranslation } from "react-i18next";

const MiniCompass = () => {
  const { t } = useTranslation("antimavic");
  
  // Hardcoded heading for now
  const heading = 83;

  // Cardinal directions are typically not translated, but we keep the structure ready for i18n
  const cardinalDirections = ["N", "E", "S", "W"];

  return (
    <div className="flex flex-col items-center justify-center bg-black/30 rounded-lg p-2 text-white">
      {/* Heading Display */}
      <div className="text-center mb-1">
        <span className="text-xl font-bold text-white">{heading}°</span>
      </div>
      
      <div className="relative w-[110px] aspect-square">
        {/* Compass Ring */}
        <div className="absolute inset-0 rounded-full border border-white/50 flex items-center justify-center">
          {/* Cardinal Direction Markers */}
          {cardinalDirections.map((dir) => (
            <div
              key={dir}
              className="absolute text-white/90 font-medium text-xs"
              style={{
                top: dir === "N" ? "3px" : dir === "S" ? "calc(100% - 15px)" : "50%",
                left: dir === "W" ? "3px" : dir === "E" ? "calc(100% - 10px)" : "50%",
                transform: "translate(-50%, -50%)",
              }}
            >
              {dir}
            </div>
          ))}
          
          {/* Direction Indicator (triangle) */}
          <div
            className="absolute left-1/2 top-1/2 text-white transform -translate-x-1/2 -translate-y-1/2"
            style={{
              fontSize: "16px",
              lineHeight: 0
            }}
          >
            ▶
          </div>
          
          {/* Red marker for North indication */}
          <div
            className="absolute top-0 left-1/2 transform -translate-x-1/2 w-0 h-0"
            style={{ 
              borderLeft: "4px solid transparent",
              borderRight: "4px solid transparent",
              borderBottom: "6px solid #ef4444",
              marginTop: "-1px"
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default MiniCompass; 