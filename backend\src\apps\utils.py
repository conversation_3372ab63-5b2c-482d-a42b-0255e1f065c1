from bson import ObjectId


def serialize_mongo_document(document):
    """
    Serialize a MongoDB document, converting ObjectId fields to strings.

    Args:
        document: A dictionary or other data structure from MongoDB.

    Returns:
        A JSON-serializable copy of the document with ObjectId fields as strings.
    """
    if not isinstance(document, dict):
        return document

    serialized = {}
    for key, value in document.items():
        if isinstance(value, ObjectId):
            serialized[key] = str(value)
        elif isinstance(value, dict):
            serialized[key] = serialize_mongo_document(value)
        elif isinstance(value, list):
            serialized[key] = [
                serialize_mongo_document(item) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            serialized[key] = value

    return serialized
