import { useState, useCallback } from "react";
import type { Device } from "../services/deviceService";
import type { Target } from "../services/targetService";

type DeleteItem = Device | Target;

interface DeleteConfirmation {
  open: boolean;
  type: "device" | "target" | null;
  item: DeleteItem | null;
}

export function useMapDeletion(
  setDevices?: React.Dispatch<React.SetStateAction<Device[]>> | null,
  setTargets?: React.Dispatch<React.SetStateAction<Target[]>> | null,
  setIsLoading?: React.Dispatch<React.SetStateAction<boolean>> | null
) {
  const [deleteConfirmation, setDeleteConfirmation] =
    useState<DeleteConfirmation>({
      open: false,
      type: null,
      item: null,
    });

  const initiateDeviceDelete = useCallback((device: Device) => {
    setDeleteConfirmation({
      open: true,
      type: "device",
      item: device,
    });
  }, []);

  const initiateTargetDelete = useCallback((target: Target) => {
    setDeleteConfirmation({
      open: true,
      type: "target",
      item: target,
    });
  }, []);

  const cancelDelete = useCallback(() => {
    setDeleteConfirmation({
      open: false,
      type: null,
      item: null,
    });
  }, []);

  // For backwards compatibility - this is only used if setters are provided
  const confirmDelete = useCallback(async () => {
    // The function handles deletion with the state setters if they're provided
    // When using React Query, this function won't be used
    if (
      !deleteConfirmation.item?._id ||
      !setDevices ||
      !setTargets ||
      !setIsLoading
    ) {
      return;
    }

    try {
      setIsLoading(true);
      if (deleteConfirmation.type === "device") {
        // This would be implemented in the calling component
      } else if (deleteConfirmation.type === "target") {
        // This would be implemented in the calling component
      }
    } catch (error) {
      console.error("Error during deletion:", error);
    } finally {
      setDeleteConfirmation({
        open: false,
        type: null,
        item: null,
      });
      setIsLoading(false);
    }
  }, [deleteConfirmation, setDevices, setTargets, setIsLoading]);

  return {
    deleteConfirmation,
    initiateDeviceDelete,
    initiateTargetDelete,
    cancelDelete,
    confirmDelete,
  };
}
