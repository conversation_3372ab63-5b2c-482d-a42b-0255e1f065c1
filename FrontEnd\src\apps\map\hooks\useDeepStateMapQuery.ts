import { useQuery } from '@tanstack/react-query';
import { fetchDeepStateMapLastFeatures, type DeepStateMap } from '../services/deepStateMapService';

export const useDeepStateMapQuery = () => {
  return useQuery<DeepStateMap, Error>({
    queryKey: ['deepStateLastFeatures'],
    queryFn: fetchDeepStateMapLastFeatures,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}; 