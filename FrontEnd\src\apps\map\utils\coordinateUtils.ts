/**
 * Utility functions for coordinate parsing and validation
 */

export interface LatLng {
  lat: number;
  lng: number;
}

/**
 * Parse coordinates from various string formats
 */
export const parseCoordinates = (coordString: string): LatLng | null => {
  if (!coordString || typeof coordString !== "string") {
    return null;
  }

  const cleanString = coordString.trim();

  // Try decimal degrees format: "49.0139, 31.2858" or "49.0139 31.2858"
  const decimalRegex = /^(-?\d+\.?\d*)[,\s]+(-?\d+\.?\d*)$/;
  const decimalMatch = cleanString.match(decimalRegex);

  if (decimalMatch) {
    const lat = parseFloat(decimalMatch[1]);
    const lng = parseFloat(decimalMatch[2]);

    if (isValidLatitude(lat) && isValidLongitude(lng)) {
      return { lat, lng };
    }
  }

  // Try DMS format: "49°00'50.0"N 31°17'08.9"E"
  const dmsRegex = /(\d+)°(\d+)'([\d.]+)"([NS])\s+(\d+)°(\d+)'([\d.]+)"([EW])/i;
  const dmsMatch = cleanString.match(dmsRegex);

  if (dmsMatch) {
    const latDeg = parseInt(dmsMatch[1]);
    const latMin = parseInt(dmsMatch[2]);
    const latSec = parseFloat(dmsMatch[3]);
    const latDir = dmsMatch[4].toUpperCase();

    const lngDeg = parseInt(dmsMatch[5]);
    const lngMin = parseInt(dmsMatch[6]);
    const lngSec = parseFloat(dmsMatch[7]);
    const lngDir = dmsMatch[8].toUpperCase();

    let lat = latDeg + latMin / 60 + latSec / 3600;
    let lng = lngDeg + lngMin / 60 + lngSec / 3600;

    if (latDir === "S") lat = -lat;
    if (lngDir === "W") lng = -lng;

    if (isValidLatitude(lat) && isValidLongitude(lng)) {
      return { lat, lng };
    }
  }

  return null;
};

/**
 * Check if a coordinate string is valid
 */
export const isValidCoordinatesString = (coordString: string): boolean => {
  return parseCoordinates(coordString) !== null;
};

/**
 * Check if latitude is valid (-90 to 90)
 */
export const isValidLatitude = (lat: number): boolean => {
  return !isNaN(lat) && lat >= -90 && lat <= 90;
};

/**
 * Check if longitude is valid (-180 to 180)
 */
export const isValidLongitude = (lng: number): boolean => {
  return !isNaN(lng) && lng >= -180 && lng <= 180;
};

/**
 * Convert LatLng to Google Maps LatLng object
 */
export const getLatLngFromString = (
  coordString: string
): google.maps.LatLng | null => {
  const coords = parseCoordinates(coordString);
  if (!coords) return null;

  return new google.maps.LatLng(coords.lat, coords.lng);
};

/**
 * Format coordinates as a readable string
 */
export const formatCoordinates = (lat: number, lng: number): string => {
  return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
};
