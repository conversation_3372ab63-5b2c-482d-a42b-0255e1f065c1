import json

import redis
from pydantic import BaseModel, ConfigDict
from redis import Redis
from src.conf import settings
from src.utils import Singleton


class RedisUnit:
    def __init__(self, redis_client: Redis, prefix: str = "", ex: int | None = 5):
        """Initialize Redis connection."""
        self._client = redis_client
        self._prefix = prefix
        self._ex = ex
        self._existing_keys = set()

    def _key_with_prefix(self, key: str) -> str:
        """Return Redis key with prefix."""
        return f"{self._prefix}:{key}"

    def set_json(self, key: str, value: dict | list) -> bool:
        """
        Store a JSON-serializable object in Redis.

        :param key: Redis key
        :param value: JSON-serializable Python object (dict, list, etc.)
        :param ex: Expiration time in seconds (optional)
        :return: True if successful, False otherwise
        """
        self._client.set(self._key_with_prefix(key), json.dumps(value), ex=self._ex)
        self._existing_keys.add(key)

    def get_data(self, key: str) -> dict | list | None:
        """
        Retrieve a JSON object from Redis.

        :param key: Redis key
        :return: Python object (dict, list, etc.) or None if key doesn't exist
        """
        data = self._client.get(self._key_with_prefix(key))
        if data:
            return json.loads(data)

    def delete(self, key: str) -> bool:
        """
        Delete a key from Redis.

        :param key: Redis key
        :return: True if deleted, False if key doesn't exist
        """
        return self._client.delete(key) > 0

    def exists(self, key: str) -> bool:
        """
        Check if a key exists in Redis.

        :param key: Redis key
        :return: True if exists, False otherwise
        """
        return self._client.exists(key) > 0

    def expire(self, key: str, time: int) -> bool:
        """
        Set expiration time for a key.

        :param key: Redis key
        :param time: Expiration time in seconds
        :return: True if successful, False otherwise
        """
        return self._client.expire(key, time)

    def get_all(self) -> dict:
        data_out = {}
        for key in self._existing_keys:
            data_out[key] = self.get_data(key)
        return data_out


class BaseModelExtra(BaseModel):
    model_config = ConfigDict(extra="allow")


class SDRModel(BaseModelExtra):
    is_active: bool = False
    status: str = "ok"
    started: int | None = None


class RedisManager(metaclass=Singleton):
    def __init__(self):
        self.client = redis.Redis(
            host=settings.redis_host,
            port=settings.redis_port,
            password=settings.redis_password,
        )
        self.station = RedisUnit(self.client, f"{settings.env_type}:station", ex=200)
        self.group = RedisUnit(self.client, f"{settings.env_type}:group", ex=20)
        self.ecoflow = RedisUnit(self.client, f"{settings.env_type}:ecoflow", ex=20)

    def get_all(self) -> dict:
        return {
            "station": self.station.get_all(),
            "group": self.group.get_all(),
        }
