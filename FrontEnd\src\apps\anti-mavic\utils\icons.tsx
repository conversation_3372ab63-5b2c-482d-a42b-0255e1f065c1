import React from "react";
import {
  Camera,
  Thermometer,
  RefreshCw,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  PictureInPicture,
  Columns,
  AlignHorizontalJustifyEnd,
  RotateCw,
  RotateCcw,
  Target,
  Circle,
  Square,
  Settings,
} from "lucide-react";

// Іконка для функції автослідкування
export const AutoTrackIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    className="w-4 h-4"
  >
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z" />
  </svg>
);

// Іконка для кнопки "вліво"
export const LeftIcon = <ChevronLeft size={20} />;

// Іконка для кнопки "вправо"
export const RightIcon = <ChevronRight size={20} />;

// Іконка для кнопки "вгору"
export const UpIcon = <ChevronUp size={20} />;

// Іконка для кнопки "вниз"
export const DownIcon = <ChevronDown size={20} />;

// Іконка для основної камери
export const MainCameraIcon = <Camera color="#4ade80" size={18} />;

// Іконка для тепловізора
export const ThermalCameraIcon = <Thermometer color="#fbbf24" size={18} />;

// Іконка для переключення камер
export const SwitchCameraIcon = <RefreshCw color="#ffffff" size={18} />;

// Іконка для режиму "картинка в картинці"
export const PipIcon = <PictureInPicture size={18} />;

// Іконка для розділеного екрану
export const SplitScreenIcon = <Columns size={18} />;

// Іконка для параметрів
export const SettingsIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    className="w-5 h-5"
  >
    <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.***********.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.***********.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" />
  </svg>
);

// Іконка для повного екрану
export const FullscreenIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    className="w-5 h-5"
  >
    <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
  </svg>
);

// Іконка для перетягування
export const DragIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    className="w-5 h-5"
  >
    <path d="M7 19h10V4H7v15zm-5-2h4V6H2v11zM18 6v11h4V6h-4z" />
  </svg>
);

// Іконка для стекового розташування камер
export const StackLayoutIcon = (
  <AlignHorizontalJustifyEnd size={18} className="rotate-90" />
);

// Camera controls
export const ResetCameraIcon = <RotateCw size={18} />;
export const TargetIcon = <Target size={20} />;

// Recording controls
export const RecordIcon = <Circle size={16} className="text-red-500" />;
export const StopIcon = <Square size={16} />;

// UI icons
export const SettingsIconAlt = <Settings size={16} />;
