import { useEffect } from "react";
import { useMap } from "@vis.gl/react-google-maps";
import type { SearchResult } from "../map-search";

interface MapControllerProps {
  searchResult: SearchResult | null;
}

export const MapController = ({ searchResult }: MapControllerProps) => {
  const map = useMap();

  useEffect(() => {
    if (map && searchResult) {
      // Pan to the search result location
      map.panTo(searchResult.position);

      // Set zoom if specified
      if (searchResult.zoom) {
        map.setZoom(searchResult.zoom);
      }
    }
  }, [map, searchResult]);

  return null; // This component doesn't render anything
};
