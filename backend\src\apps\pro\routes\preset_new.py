from fastapi import APIRouter, HTTPException, Response
from src.apps.auth.deps import AdminDep, UserDep
from src.apps.deps import ObjID_Dep

from ..models import (
    Preset,
    PresetDB,
    PresetScreen,
    PresetScreenDB,
    QuickStart,
    QuickStartDB,
)

router = APIRouter()


router = APIRouter()
quick_router = APIRouter(prefix="/quick_start")
screen_router = APIRouter(prefix="/screen")


@router.get("/")
async def get_all_presets():
    return await PresetDB.find().to_list()


@router.get("/{_id}")
async def get_one(_id: ObjID_Dep) -> PresetDB | None:
    return await PresetDB.find_one(_id, fetch_links=True)


@router.post("/", response_model=PresetDB)
async def create_preset(user: UserDep, new_preset: Preset):
    if not new_preset.org:
        new_preset.org = user.org
    preset = PresetDB(**new_preset.model_dump())
    await preset.create()
    return preset


@router.patch("/{_id}")
async def update_preset(_id: ObjID_Dep, new_preset: dict):
    preset = await PresetDB.find_one(_id)
    if not preset:
        raise HTTPException(404, "preset not found")

    preset = preset.model_copy(update=new_preset)
    await preset.save()


@router.delete("/")
async def delete_preset(_: AdminDep, _id: ObjID_Dep) -> Response:
    await PresetDB.find_one(_id).delete()
    return Response(status_code=204)


"--------------------------- QUICK START ---------------------------"


@quick_router.get("/")
async def get_all_presets_quick():
    return await QuickStartDB.find().to_list()


@quick_router.get("/{_id}", response_model=QuickStartDB)
async def get_quick(_id: ObjID_Dep):
    return await QuickStartDB.find_one(_id, fetch_links=True)


@quick_router.post("/", response_model=QuickStartDB)
async def create_preset_quick(new_preset: QuickStart):
    preset = QuickStartDB(**new_preset.model_dump())
    await preset.create()
    return preset


@quick_router.patch("/{_id}")
async def update_preset_quick(_id: ObjID_Dep, new_preset: dict):
    preset = await QuickStartDB.find_one(_id)
    if not preset:
        raise HTTPException(404, "preset not found")

    preset = preset.model_copy(update=new_preset)
    await preset.save()


@quick_router.delete("/")
async def delete_preset_quick(_: AdminDep, _id: ObjID_Dep) -> Response:
    await QuickStartDB.find_one(_id).delete()
    return Response(status_code=204)


"--------------------------- SCREEN PRESET ---------------------------"


@screen_router.get("/")
async def get_all_presets_screen():
    return await PresetScreenDB.find().to_list()


@screen_router.get("/{_id}", response_model=PresetScreenDB)
async def get_screen(_id: ObjID_Dep):
    return await PresetScreenDB.find_one(_id, fetch_links=True)


@screen_router.post("/", response_model=PresetScreenDB)
async def create_preset_screen(new_preset: PresetScreen):
    preset = PresetScreenDB(**new_preset.model_dump())
    await preset.create()
    return preset


@screen_router.patch("/{_id}")
async def update_preset_screen(_id: ObjID_Dep, new_preset: dict):
    preset = await PresetScreenDB.find_one(_id)
    if not preset:
        raise HTTPException(404, "preset not found")

    preset = preset.model_copy(update=new_preset)
    await preset.save()


@screen_router.delete("/")
async def delete_preset_screen(_: AdminDep, _id: ObjID_Dep) -> Response:
    await PresetScreenDB.find_one(_id).delete()
    return Response(status_code=204)


router.include_router(quick_router)
router.include_router(screen_router)
