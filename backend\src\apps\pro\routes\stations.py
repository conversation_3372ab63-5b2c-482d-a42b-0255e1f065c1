from fastapi import APIRouter, HTTPException, Response
from src.apps.auth.deps import AdminDep, UserDep
from src.apps.deps import ObjID_Dep
from src.conf.loader import get_motor_manager

from ..models import Station, StationDB

router = APIRouter()

db = get_motor_manager()

router = APIRouter()


@router.get("/")
async def get_all_stations():
    return await StationDB.find().to_list()


@router.get("/{_id}")
async def get_one(_id: ObjID_Dep) -> StationDB | None:
    return await StationDB.find_one(_id, fetch_links=True)


@router.post("/", response_model=StationDB)
async def create_station(user: UserDep, new_station: Station):
    if not new_station.org:
        new_station.org = user.org
    station = StationDB(**new_station.model_dump())
    await station.create()
    return station


@router.put("/{_id}")
async def update_station(_id: ObjID_Dep, new_station: dict):
    station = await StationDB.find_one(_id)
    if not station:
        raise HTTPException(404, "station not found")

    station = station.model_copy(update=new_station)
    await station.save()


@router.delete("/{_id}", status_code=204)
async def delete_station(_id: ObjID_Dep, _: AdminDep) -> Response:
    await StationDB.find_one(_id).delete()
