from typing import List

from beanie import Document, Link, PydanticObjectId
from pydantic import BaseModel
from src.apps.auth.models import OrgDB, UserDB

from . import UnitDB


class Station(BaseModel):
    name: str
    description: str
    latitude: int
    longitude: int
    org: PydanticObjectId | None = None
    ecoflow_id: str | None = None
    groups: List[Link[UnitDB]] = []
    users: List[Link[UserDB]] = []


class StationDB(Document, Station):
    org: Link[OrgDB]

    class Settings:
        name = "pro.stations"


class StationGroup(BaseModel):
    name: str
    stations: List[Link[StationDB]] = []


class StationGroupDB(Document, StationGroup):
    class Settings:
        name = "station_group"
