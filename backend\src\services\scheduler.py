import asyncio
import traceback
from typing import Callable

from loguru import logger


class AsyncScheduler:
    def __init__(self):
        self.tasks: list[asyncio.Task] = []
        self.loop = asyncio.get_event_loop()  # Get the current event loop

    def add_task(self, task: Callable[[], None], time_interval: float = 60, task_name: str | None = None) -> str:
        """
        Add a periodic task to the scheduler and run it in the current event loop.
        
        Args:
            task: The async function to run periodically.
            time_interval: Time in seconds between runs (default: 60).
            task_name: Optional name for the task (defaults to function name).
        
        Returns:
            The task name for reference.
        """
        if task_name is None:
            task_name = task.__name__

        async def periodic_task():
            while True:
                try:
                    await task()  # Execute the task
                    await asyncio.sleep(time_interval)  # Wait before next run
                except asyncio.CancelledError:
                    break  # Exit on cancellation
                except Exception as e:
                    logger.error(f"Error in task {task_name}: {e}")
                    await asyncio.sleep(time_interval)
                    print(traceback.format_exc())

        # Create and store the task in the current event loop
        task_obj = self.loop.create_task(periodic_task())
        self.tasks.append(task_obj)
        return task_name

    def stop_task(self, task_index: int) -> None:
        """
        Stop a specific task by its index in the tasks list.
        
        Args:
            task_index: Index of the task to stop.
        """
        if 0 <= task_index < len(self.tasks):
            self.tasks[task_index].cancel()
            del self.tasks[task_index]

    def stop_all_tasks(self) -> None:
        """Stop all scheduled tasks."""
        for task in self.tasks:
            task.cancel()
        self.tasks.clear()

