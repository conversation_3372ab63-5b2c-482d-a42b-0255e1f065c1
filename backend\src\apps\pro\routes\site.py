from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from src.conf.settings import dirs

router = APIRouter()


templates = Jinja2Templates(directory=dirs.app_templates)


@router.get("/state", response_class=HTMLResponse)
async def read_item(request: Request):
    ws_url = f"ws://{request.url.netloc}/api/ws/station"
    print(ws_url)
    return templates.TemplateResponse(
        request=request, name="state.html", context={"request": request, "ws_url": ws_url}
    )