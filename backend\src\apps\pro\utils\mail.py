import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from src.conf.settings import settings


def send_email(
    subject,
    body,
    to=settings.admin_email,
    sender_email=settings.admin_email,
    password=settings.admin_email_app_password,
    smtp_port=465,
    smtp_server="smtp.ukr.net",
):
    """
    Send an email through Ukr.net's SMTP server.

    Args:
        sender_email (str): Sender's Ukr.net email address.
        to (str): Recipient's email address.
        subject (str): Subject of the email.
        body (str): Body of the email.
        smtp_server (str): SMTP server address (default: smtp.ukr.net).
        smtp_port (int): SMTP port number (default: 465).
        password (str): Password generated for IMAP access (not your regular email password).

    Returns:
        str: Success or error message.
    """
    try:
        # Create the email message
        msg = MIMEMultipart()
        msg["From"] = sender_email
        msg["To"] = to
        msg["Subject"] = subject
        msg.attach(MIMEText(body, "plain"))

        # Set up the SMTP server with SSL directly
        server = smtplib.SMTP_SSL(
            smtp_server, smtp_port
        )  # Use SSL directly on port 465

        # Log in to the Ukr.net account
        server.login(sender_email, password)

        # Send the email
        server.sendmail(sender_email, to, msg.as_string())

        # Close the connection
        server.quit()

        return f"Email successfully sent to {to}"

    except Exception as e:
        return f"Error: {str(e)}"


if __name__ == "__main__":
    # Example usage:
    sender_email = "<EMAIL>"
    recipient_email = "<EMAIL>"
    subject = "Test Email"
    body = "Hello, this is a test email sent via Python."
    password = "lSICzI9rPUr9iojc"  # Use the password generated for IMAP access

    send_email(subject, body)
