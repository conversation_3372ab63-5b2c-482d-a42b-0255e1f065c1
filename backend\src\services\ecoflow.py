import asyncio
import hashlib
import hmac
import random
import time

import httpx
from loguru import logger
from src.conf.settings import settings


def _flatten_params(data, prefix=""):
    items = []
    if isinstance(data, dict):
        for key in sorted(data):  # ASCII sorting of keys
            new_prefix = f"{prefix}.{key}" if prefix else key
            items.extend(_flatten_params(data[key], new_prefix))
    elif isinstance(data, list):
        for index, value in enumerate(data):
            new_prefix = f"{prefix}[{index}]"
            items.extend(_flatten_params(value, new_prefix))
    else:
        items.append(f"{prefix}={data}")
    return items


def _filter_response_fields(data: dict) -> dict:
    fields = ["pd.wattsOutSum"]
    d = data["data"]
    return {f: d[f] for f in fields if f in d}

class Ecoflow:
    def __init__(self, ecoflow_device_id: str | int):
        self.device_id = ecoflow_device_id
        self.access_key = settings.ecoflow_access_key
        self.secret_key = settings.ecoflow_secret_key
        self.base_url = "https://api-e.ecoflow.com/iot-open/sign/device/quota"

    def _create_sign(self, payload) -> tuple[str, str, str]:
        n = str(random.randint(100000, 999999))
        t = str(int(time.time() * 1000))

        params_string = "&".join(_flatten_params(payload))
        params_string += f"&accessKey={self.access_key}&nonce={n}&timestamp={t}"

        sign = hmac.new(
            self.secret_key.encode(), params_string.encode(), hashlib.sha256
        ).hexdigest()

        return sign, n, t

    def _get_payload_and_headers(self, params: dict | None = None) -> tuple[dict, dict]:
        payload = {"sn": self.device_id}
        if params:
            payload["params"] = params

        sign, nonce, timestamp = self._create_sign(payload)

        headers = {
            "accessKey": self.access_key,
            "timestamp": timestamp,
            "nonce": nonce,
            "sign": sign,
        }

        return payload, headers

    async def get_device_info(self):
        payload, headers = self._get_payload_and_headers()

        async with httpx.AsyncClient() as client:
            response = await client.get(
                self.base_url + "/all", headers=headers, params=payload
            )
            try:
                resp = _filter_response_fields(response.json())
                logger.debug(f"ecoflow resp: {resp}")
                return resp
            except:
                logger.error(response.json())

    async def reload(self, sec_delay: int = 5):
        logger.debug("Reloading ecoflow device...")
        params = {"cmdSet": 32, "id": 66}
        payload_off, headers = self._get_payload_and_headers(
            params | {"inv.cfgAcEnabled": 0}
        )
        async with httpx.AsyncClient() as client:
            response_off = await client.put(
                self.base_url, headers=headers, json=payload_off
            )
            print(
                "🔹 Відключення інвертора:",
                response_off.status_code,
                response_off.json(),
            )

            await asyncio.sleep(sec_delay)

            payload_on, headers = self._get_payload_and_headers(
                params | {"inv.cfgAcEnabled": 1}
            )

            response_on = await client.put(self.base_url, headers=headers, json=payload_on)
            print(
                "🔹 Увімкнення інвертора:", response_on.status_code, response_on.json()
            )
