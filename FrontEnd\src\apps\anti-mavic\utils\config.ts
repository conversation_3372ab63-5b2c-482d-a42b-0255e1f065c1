// Camera stream URLs
export const CAMERA_URLS = {
  main:
    import.meta.env.VITE_MAIN_CAMERA_URL ||
    "http://100.66.150.79:8889/main_cam/",
  thermal:
    import.meta.env.VITE_THERMAL_CAMERA_URL ||
    "http://100.66.150.79:8889/thermal/",
};

// Camera specifications
export const CAMERA_SPECS = {
  main: {
    width: 2688,
    height: 1520,
    aspectRatio: 2688 / 1520, // ≈ 1.768
  },
  thermal: {
    width: 1280,
    height: 720,
    aspectRatio: 1280 / 720, // ≈ 1.778
  },
};

// Layout configuration for responsive design
export const LAYOUT_CONFIG = {
  // Default panel sizes
  defaultSidePanelWidth: 320, // 20rem
  defaultBottomPanelHeight: 200, // 12.5rem

  // Responsive breakpoints for aspect ratios
  aspectRatioBreakpoints: {
    ultraWide: 2.5, // > 2.5:1 (e.g., 21:9)
    wide: 1.9, // > 1.9:1 (e.g., 16:9)
    standard: 1.5, // > 1.5:1 (e.g., 3:2)
    square: 1.1, // > 1.1:1 (close to square)
    portrait: 0.9, // < 0.9:1 (portrait)
  },

  // Panel size adjustments based on aspect ratio
  panelAdjustments: {
    ultraWide: {
      sidePanelWidthMultiplier: 0.8, // Reduce side panel for ultra-wide
      bottomPanelHeightMultiplier: 1.3, // Increase bottom panel
    },
    wide: {
      sidePanelWidthMultiplier: 1.0, // Standard size
      bottomPanelHeightMultiplier: 1.0, // Standard size
    },
    standard: {
      sidePanelWidthMultiplier: 1.1, // Slightly larger side panel
      bottomPanelHeightMultiplier: 0.9, // Slightly smaller bottom panel
    },
    square: {
      sidePanelWidthMultiplier: 1.2, // Larger side panel for square videos
      bottomPanelHeightMultiplier: 0.8, // Smaller bottom panel
    },
    portrait: {
      sidePanelWidthMultiplier: 1.4, // Much larger side panel for portrait
      bottomPanelHeightMultiplier: 0.7, // Smaller bottom panel
    },
  },

  // Minimum and maximum panel sizes
  constraints: {
    sidePanelMinWidth: 280,
    sidePanelMaxWidth: 450,
    bottomPanelMinHeight: 160,
    bottomPanelMaxHeight: 300,
  },
};
