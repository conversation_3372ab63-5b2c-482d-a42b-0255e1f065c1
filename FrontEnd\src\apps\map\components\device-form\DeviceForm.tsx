import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X } from "lucide-react";
import type { Device } from "../../services/deviceService";

interface DeviceFormProps {
  isOpen: boolean;
  initialData?: Device | null;
  isEdit?: boolean;
  onClose: () => void;
  onSave: (device: Omit<Device, "id">) => void;
}

export const DeviceForm = ({
  isOpen,
  initialData,
  isEdit = false,
  onClose,
  onSave,
}: DeviceFormProps) => {
  const { t } = useTranslation("map");
  const [formData, setFormData] = useState<Omit<Device, "id">>({
    lat: 0,
    lng: 0,
    name: "",
    type: "",
    distance: 5,
    coverageAngle: 120,
    azimuth: 0,
    verticalAngleLimit: 45,
    color: "#1E90FF", // Default to blue
    mode: "suppression",
    state: "active",
  });

  // Add coordinates string state for display
  const [coordinatesStr, setCoordinatesStr] = useState("");

  // Initialize form with initial data if provided
  useEffect(() => {
    if (initialData) {
      setFormData({
        lat: initialData.lat,
        lng: initialData.lng,
        name: initialData.name || "",
        type: initialData.type || "",
        distance: initialData.distance || 5,
        coverageAngle: initialData.coverageAngle || 120,
        azimuth: initialData.azimuth || 0,
        verticalAngleLimit: initialData.verticalAngleLimit || 45,
        color: initialData.color || "#1E90FF",
        mode: initialData.mode || "suppression",
        state: initialData.state || "active",
      });
      // Set coordinates string
      setCoordinatesStr(`${initialData.lat}, ${initialData.lng}`);
    }
  }, [initialData]);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    if (name === "coordinates") {
      setCoordinatesStr(value);
      // Parse coordinates from comma-separated string
      const coords = value.split(",").map(coord => coord.trim());
      if (coords.length === 2) {
        const lat = parseFloat(coords[0]) || 0;
        const lng = parseFloat(coords[1]) || 0;
        setFormData(prev => ({ ...prev, lat, lng }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSliderChange =
    (name: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = parseFloat(e.target.value);
      setFormData((prev) => ({ ...prev, [name]: value }));
    };

  const handleColorSelect = (color: string) => {
    setFormData((prev) => ({ ...prev, color }));
  };

  const handleToggleChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Ensure lat and lng are numbers before saving
    const formattedData = {
      ...formData,
      lat:
        typeof formData.lat === "string"
          ? parseFloat(formData.lat) || 0
          : formData.lat,
      lng:
        typeof formData.lng === "string"
          ? parseFloat(formData.lng) || 0
          : formData.lng,
    };

    onSave(formattedData);
  };

  if (!isOpen) return null;

  const colors = [
    "#1E90FF", // Blue
    "#8A2BE2", // Purple
    "#FF00FF", // Magenta
    "#008000", // Green
    "#20B2AA", // Light Sea Green
    "#ADFF2F", // Green Yellow
    "#00FF7F", // Spring Green
    "#FF8C00", // Dark Orange
    "#FFD700", // Gold
    "#DC143C", // Crimson
    "#A0522D", // Sienna
    "#C0C0C0", // Silver
    "#708090", // Slate Grey
  ];

  return (
    <div className="fixed right-0 top-0 h-full w-[380px] bg-[--gray-9] dark:bg-[--gray-8] shadow-lg z-20 overflow-y-auto">
      <div className="flex items-center justify-between p-4 border-b border-[--gray-3] dark:border-[--gray-7]">
        <h2 className="text-lg font-medium text-[--gray-1] dark:text-[--gray-1]">
          {isEdit ? t("edit_device") : t("new_device")}
        </h2>
        <button
          className="p-1 rounded-full hover:bg-[--gray-8] dark:hover:bg-[--gray-7] text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
          onClick={onClose}
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("name")}
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
            placeholder={t("enter_name")}
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("coordinates")}
          </label>
          <input
            type="text"
            name="coordinates"
            value={coordinatesStr}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
            placeholder="Latitude, Longitude"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("type")}
          </label>
          <select
            name="type"
            value={formData.type}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
          >
            <option value="">{t("select_device_type")}</option>
            <option value="radar">Radar</option>
            <option value="jammer">Jammer</option>
            <option value="drone">Drone</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("group")}
          </label>
          <select className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]">
            <option value="">{t("select_device_group")}</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("distance")} (km)
          </label>
          <input
            type="range"
            min="0"
            max="20"
            step="0.1"
            value={formData.distance}
            onChange={handleSliderChange("distance")}
            className="w-full accent-[--green-7] dark:accent-[--green-5]"
          />
          <input
            type="number"
            name="distance"
            value={formData.distance}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("coverage_angle")}
          </label>
          <input
            type="range"
            min="0"
            max="360"
            step="1"
            value={formData.coverageAngle}
            onChange={handleSliderChange("coverageAngle")}
            className="w-full accent-[--green-7] dark:accent-[--green-5]"
          />
          <input
            type="number"
            name="coverageAngle"
            value={formData.coverageAngle}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("azimuth")}
          </label>
          <input
            type="range"
            min="0"
            max="360"
            step="1"
            value={formData.azimuth}
            onChange={handleSliderChange("azimuth")}
            className="w-full accent-[--green-7] dark:accent-[--green-5]"
          />
          <input
            type="number"
            name="azimuth"
            value={formData.azimuth}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("vertical_angle_limit")}
          </label>
          <input
            type="range"
            min="0"
            max="90"
            step="1"
            value={formData.verticalAngleLimit}
            onChange={handleSliderChange("verticalAngleLimit")}
            className="w-full accent-[--green-7] dark:accent-[--green-5]"
          />
          <input
            type="number"
            name="verticalAngleLimit"
            value={formData.verticalAngleLimit}
            onChange={handleChange}
            className="w-full p-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md bg-[--gray-9] dark:bg-[--gray-9] text-[--gray-1] dark:text-[--gray-1]"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
            {t("color")}
          </label>
          <div className="flex flex-wrap gap-2">
            {colors.map((color) => (
              <button
                key={color}
                type="button"
                className={`w-8 h-8 rounded-full ${
                  formData.color === color
                    ? "ring-2 ring-[--green-7] dark:ring-[--green-5]"
                    : ""
                }`}
                style={{ backgroundColor: color }}
                onClick={() => handleColorSelect(color)}
              />
            ))}
          </div>
        </div>

        <div className="space-y-4 pt-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
              {t("mode")}: {formData.mode ? t(formData.mode) : t("normal")}
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={formData.mode === "suppression"}
                onChange={() =>
                  handleToggleChange(
                    "mode",
                    formData.mode === "suppression" ? "normal" : "suppression"
                  )
                }
              />
              <div className="w-11 h-6 bg-[--gray-3] peer-focus:outline-none rounded-full peer dark:bg-[--gray-7] peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-[--gray-3] after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-[--gray-6] peer-checked:bg-[--green-7] dark:peer-checked:bg-[--green-5]"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-[--gray-1] dark:text-[--gray-1]">
              {t("state")}: {formData.state ? t(formData.state) : t("inactive")}
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={formData.state === "active"}
                onChange={() =>
                  handleToggleChange(
                    "state",
                    formData.state === "active" ? "inactive" : "active"
                  )
                }
              />
              <div className="w-11 h-6 bg-[--gray-3] peer-focus:outline-none rounded-full peer dark:bg-[--gray-7] peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-[--gray-3] after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-[--gray-6] peer-checked:bg-[--green-7] dark:peer-checked:bg-[--green-5]"></div>
            </label>
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-[--gray-3] dark:border-[--gray-6] rounded-md text-[--gray-1] dark:text-[--gray-1] hover:bg-[--gray-8] dark:hover:bg-[--gray-7] transition-colors duration-200"
          >
            {t("cancel")}
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[--green-7] hover:bg-[--green-6] dark:bg-[--green-5] dark:hover:bg-[--green-4] text-white rounded-md transition-colors duration-200"
          >
            {isEdit ? t("edit") : t("add")}
          </button>
        </div>
      </form>
    </div>
  );
};
