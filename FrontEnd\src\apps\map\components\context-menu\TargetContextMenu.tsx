import { useTranslation } from "react-i18next";
import { Edit, Trash } from "lucide-react";
import type { Target } from "../../services/targetService";

export interface TargetContextMenuProps {
  open: boolean;
  target: Target | null;
  position: { x: number; y: number };
  onEdit?: (target: Target) => void;
  onDelete?: (target: Target) => void;
  onMove?: (target: Target) => void;
  onClose?: () => void;
}

export const TargetContextMenu = ({
  open,
  target,
  position,
  onEdit,
  onDelete,
  onMove,
  onClose,
}: TargetContextMenuProps) => {
  const { t } = useTranslation("map");

  if (!open || !target) return null;

  const handleAction = (callback?: (target: Target) => void) => {
    if (callback && target) {
      callback(target);
    }
    if (onClose) onClose();
  };

  return (
    <div
      className="fixed z-50 min-w-[180px] bg-[--gray-9] dark:bg-[--gray-8] rounded-md overflow-hidden shadow-md"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="p-2 text-sm font-medium border-b border-[--gray-3] dark:border-[--gray-7] text-[--red-6] dark:text-[--red-5]">
        {target.name || "Target"}
      </div>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
        onClick={() => handleAction(onEdit)}
      >
        <Edit className="mr-2 h-4 w-4" />
        {t("edit")}
      </button>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--gray-2] dark:text-[--gray-1] transition-colors duration-150"
        onClick={() => handleAction(onMove)}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="mr-2 h-4 w-4"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M5 9l-3 3 3 3"></path>
          <path d="M9 5l3-3 3 3"></path>
          <path d="M15 19l3 3 3-3"></path>
          <path d="M19 9l3 3-3 3"></path>
          <path d="M2 12h20"></path>
          <path d="M12 2v20"></path>
        </svg>
        {t("move")}
      </button>

      <div className="h-px bg-[--gray-3] dark:bg-[--gray-7]"></div>

      <button
        className="flex w-full items-center p-2 text-sm hover:bg-[--gray-8] dark:hover:bg-[--gray-7] cursor-pointer text-[--red-6] dark:text-[--red-5] transition-colors duration-150"
        onClick={() => handleAction(onDelete)}
      >
        <Trash className="mr-2 h-4 w-4" />
        {t("delete")}
      </button>
    </div>
  );
};
