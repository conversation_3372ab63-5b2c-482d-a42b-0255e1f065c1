import { useState, useCallback } from "react";
import type { SearchResult } from "../components/map-search";

export interface UseMapSearchReturn {
  isSearchDialogOpen: boolean;
  searchResult: SearchResult | null;
  openSearchDialog: () => void;
  closeSearchDialog: () => void;
  setSearchResult: (result: SearchResult) => void;
  clearSearchResult: () => void;
  panToSearchResult: (map: google.maps.Map | null) => void;
}

export const useMapSearch = (): UseMapSearchReturn => {
  const [isSearchDialogOpen, setIsSearchDialogOpen] = useState(false);
  const [searchResult, setSearchResultState] = useState<SearchResult | null>(
    null
  );

  const openSearchDialog = useCallback(() => {
    setIsSearchDialogOpen(true);
  }, []);

  const closeSearchDialog = useCallback(() => {
    setIsSearchDialogOpen(false);
  }, []);

  const setSearchResult = useCallback((result: SearchResult) => {
    setSearchResultState(result);
  }, []);

  const clearSearchResult = useCallback(() => {
    setSearchResultState(null);
  }, []);

  const panToSearchResult = useCallback(
    (map: google.maps.Map | null) => {
      if (!map || !searchResult) return;

      // Pan to the search result location
      map.panTo(searchResult.position);

      // Set zoom if specified
      if (searchResult.zoom) {
        map.setZoom(searchResult.zoom);
      }
    },
    [searchResult]
  );

  return {
    isSearchDialogOpen,
    searchResult,
    openSearchDialog,
    closeSearchDialog,
    setSearchResult,
    clearSearchResult,
    panToSearchResult,
  };
};
