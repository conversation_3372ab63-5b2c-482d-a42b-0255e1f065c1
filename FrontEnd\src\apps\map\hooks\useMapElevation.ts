"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import { useMap } from "@vis.gl/react-google-maps";
import { getLatLngFromString } from "../utils/coordinates";

export interface ElevationService {
  getElevationForLocations: (request: {
    locations: google.maps.LatLngLiteral[];
  }) => Promise<{ results: { elevation: number }[] }>;
}

export const useMapElevation = () => {
  const [elevationService, setElevationService] = useState<
    ElevationService | undefined
  >(undefined);

  // Use the map hook to ensure we're within the Google Maps context
  const map = useMap();

  const getElevation = useCallback(
    async (latLng: google.maps.LatLngLiteral | string) => {
      let location: google.maps.LatLngLiteral | undefined;

      // Handle string coordinates
      if (typeof latLng === "string") {
        location = getLatLngFromString(latLng);
      } else {
        location = latLng;
      }

      if (!elevationService) {
        return undefined;
      }

      if (!location) {
        return undefined;
      }

      try {
        const result = await elevationService.getElevationForLocations({
          locations: [location],
        });

        const value = result.results?.at(0)?.elevation;

        const roundedValue = value ? Math.round(value) : undefined;

        return roundedValue;
      } catch (error) {
        console.error("getElevation: Error fetching elevation:", error);
        return undefined;
      }
    },
    [elevationService]
  );

  useEffect(() => {
    // We need both the map instance (to ensure we're in the right context) and google to be available
    if (!map) {
      return;
    }

    if (typeof google === "undefined") {
      return;
    }

    if (!google.maps) {
      return;
    }

    if (!google.maps.ElevationService) {
      return;
    }

    if (elevationService) {
      return;
    }

    try {
      const newService = new google.maps.ElevationService();

      setElevationService(newService);
    } catch (error) {
      console.error("useMapElevation: Error creating ElevationService:", error);
    }
  }, [map, elevationService]);

  return useMemo(() => {
    const result = {
      getElevation,
      isReady: !!elevationService,
    };

    return result;
  }, [getElevation, elevationService]);
};
