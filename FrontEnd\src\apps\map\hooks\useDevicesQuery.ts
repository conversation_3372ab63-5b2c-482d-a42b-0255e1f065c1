import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { deviceService } from "../services/deviceService";
import type { Device } from "../services/deviceService";

export const DEVICES_QUERY_KEY = "devices";

export function useDevicesQuery() {
  return useQuery({
    queryKey: [DEVICES_QUERY_KEY],
    queryFn: () => deviceService.getAllDevices(),
  });
}

export function useCreateDeviceMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (device: Omit<Device, "id">) =>
      deviceService.createDevice(device),
    onSuccess: (newDevice) => {
      queryClient.setQueryData<Device[]>(
        [DEVICES_QUERY_KEY],
        (oldDevices = []) => [...oldDevices, newDevice as Device]
      );
    },
  });
}

export function useUpdateDeviceMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, device }: { id: string; device: Partial<Device> }) =>
      deviceService.updateDevice(id, device),
    onMutate: async ({ id, device }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: [DEVICES_QUERY_KEY] });

      // Snapshot the previous value
      const previousDevices = queryClient.getQueryData<Device[]>([
        DEVICES_QUERY_KEY,
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData<Device[]>(
        [DEVICES_QUERY_KEY],
        (oldDevices = []) => {
          return oldDevices.map((d) => {
            if (d._id === id) {
              return { ...d, ...device, _id: id };
            }
            return d;
          });
        }
      );

      return { previousDevices };
    },
    onError: (err, { id }, context) => {
      // If there was an error, roll back to the previous value
      if (context?.previousDevices) {
        queryClient.setQueryData([DEVICES_QUERY_KEY], context.previousDevices);
      }
    },
    onSettled: () => {
      // Refetch after error or success
      queryClient.invalidateQueries({ queryKey: [DEVICES_QUERY_KEY] });
    },
  });
}

export function useDeleteDeviceMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deviceService.deleteDevice(id),
    onSuccess: (_, id) => {
      queryClient.setQueryData<Device[]>(
        [DEVICES_QUERY_KEY],
        (oldDevices = []) => oldDevices.filter((d) => d._id !== id)
      );
    },
  });
}
