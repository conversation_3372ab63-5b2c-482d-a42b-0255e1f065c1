import pytest
from src.apps.utils.calculation import SweepCalculator


class TestSweepCalculator:
    """Unit tests for SweepCalculator class."""

    def test_init_with_bandwidth(self):
        """Test initialization with bandwidth (auto-calculate sweeps)."""
        calculator = SweepCalculator(
            start_frequency=1000,
            end_frequency=2000,
            bw=500,
            sweep_time=50
        )
        
        assert calculator.start_frequency == 1000
        assert calculator.end_frequency == 2000
        assert calculator.bw == 500
        assert calculator.sweep_number == 2
        assert calculator.time == 50
        assert calculator._sweeps == [[1000, 1500], [1500, 2000]]

    def test_init_without_bandwidth_raises_error(self):
        """Test initialization without bandwidth raises ValueError."""
        with pytest.raises(ValueError, match="Bandwidth is required"):
            SweepCalculator(start_frequency=1000, end_frequency=2000)

    def test_init_with_sweep_params(self):
        """Test initialization with pre-defined sweep_params."""
        calculator = SweepCalculator(
            start_frequency=0,  # Ignored when sweep_params is provided
            end_frequency=0,    # Ignored when sweep_params is provided
            sweep_params=[
                {"start": "100", "end": "200"},
                {"start": "300", "end": "400"}
            ]
        )
        
        assert calculator._sweeps == [[100, 200], [300, 400]]

    def test_sweeps_property(self):
        """Test the sweeps property formatting."""
        calculator = SweepCalculator(
            start_frequency=1000,
            end_frequency=2000,
            bw=500
        )
        
        # Expected: [(center, step)] for each sweep
        assert calculator.sweeps == [
            [1250, 500],  # (1000 + 250, 500)
            [1750, 500]   # (1500 + 250, 500)
        ]

    def test_get_sweeps_data(self):
        """Test get_sweeps_data output structure."""
        calculator = SweepCalculator(
            start_frequency=1000,
            end_frequency=2000,
            bw=500,
            sweep_time=50
        )
        
        assert calculator.get_sweeps_data() == {
            "params": [[1250, 500], [1750, 500]],
            "time": 50
        }

    def test_add_sweeps_dict(self):
        """Test dynamically adding sweeps via dictionary."""
        
        params = [
            {"start": "500", "end": "550"},
            {"start": "550", "end": "600"},
        ]
        calculator = SweepCalculator(
            start_frequency=0, 
            end_frequency=0,
            bw=50,
            sweep_params=params
        )
        assert calculator.sweeps == [[525, 50], [575, 50]]
        

    def test_edge_case_single_sweep(self):
        """Test when bandwidth covers entire frequency range."""
        calculator = SweepCalculator(
            start_frequency=1000,
            end_frequency=2000,
            bw=1000
        )
        
        assert calculator.sweep_number == 1
        assert calculator._sweeps == [[1000, 2000]]
        assert calculator.sweeps == [[1500, 1000]]

    def test_edge_case_uneven_division(self):
        """Test when frequency range isn't perfectly divisible by bandwidth."""
        calculator = SweepCalculator(
            start_frequency=1000,
            end_frequency=2001,  # 1001 units (not divisible by 500)
            bw=500
        )
        
        # Expected: 3 sweeps (500 + 500 + 1)
        assert calculator.sweep_number == 3
        assert calculator.bw == 334  # ceil(1001 / 3) = 334
        assert calculator._sweeps == [
            [1000, 1334],
            [1334, 1668],
            [1668, 2001]
        ]

    def test_non_integer_string_input_in_sweep_params(self):
        """Test sweep_params with stringified numbers."""
        calculator = SweepCalculator(
            start_frequency=0,
            end_frequency=0,
            sweep_params=[
                {"start": "100", "end": "200"},
                {"start": "300", "end": "400"}
            ]
        )
        
        assert calculator._sweeps == [[100, 200], [300, 400]]
