from typing import Any, List

import influxdb_client_3 as InfluxDBClient3
from influxdb_client_3 import (
    InfluxDBError,
    Point,
    WriteOptions,
    write_client_options,
)
from loguru import logger
from src.conf.settings import settings


class BatchingCallback:
    def __init__(self):
        self.write_count = 0

    def success(self, conf, data: str) -> None:
        self.write_count += 1
        # logger.info(f"Written batch: {conf}, data: {data}")

    def error(self, conf, data: str, exception: InfluxDBError) -> None:
        logger.error(f"Cannot write batch: {conf}, data: {data} due: {exception}")

    def retry(self, conf, data: str, exception: InfluxDBError) -> None:
        logger.warning(f"Retryable error occurs for batch: {conf}, data: {data} retry: {exception}")


class TSDBClient:
    _instance: "TSDBClient" = None

    @classmethod
    def init(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    @classmethod
    def get_instance(cls) -> "TSDBClient":
        """Get the singleton instance of the TSDBClient."""
        if not cls._instance:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        # Default write options if not provided
        write_options = WriteOptions(
            batch_size=100,
            flush_interval=3_000,
        )

        self.callback = BatchingCallback()
        self._write_client_options = write_client_options(
            success_callback=self.callback.success,
            error_callback=self.callback.error,
            retry_callback=self.callback.retry,
            write_options=write_options
        )

        # Initialize the persistent client
        self._client = None
        self._initialize_client()

    def _initialize_client(self) -> None:
        """Initialize or reinitialize the InfluxDB client."""
        try:
            if self._client is not None:
                self._client.close()  # Close existing client if present
            self._client = InfluxDBClient3.InfluxDBClient3(
                host=settings.influxdb_con_url,
                database=settings.influxdb_database,
                write_client_options=self._write_client_options,
                token=settings.influxdb_token,
                org=settings.influxdb_org,
            )
            logger.info("InfluxDB client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize InfluxDB client: {str(e)}")
            self._client = None
            raise

    def _safe_execute(self, cb) -> Any:
        """Ensure the client is available, reinitializing if necessary."""
        if self._client is None:
            logger.warning("Client is None, attempting to reinitialize")
            self._initialize_client()
        try:
            return cb()
        except Exception as e:
            logger.warning(f"Client health check failed: {str(e)}, reinitializing")
            self._initialize_client()

    def write(self, points: List[Point]) -> None:
        """Write points to the database."""
        try:

            def cb():
                self._client.write(
                    record=points, write_precision=settings.influxdb_write_precision
                )

            self._safe_execute(cb)
        except InfluxDBError as e:
            logger.error(f"Failed to write points: {str(e)}")
            self._client = None  # Mark client as invalid
            raise
        except Exception as e:
            logger.error(f"Unexpected error during write: {str(e)}")
            self._client = None  # Mark client as invalid
            raise

    def query(self, query: str):
        """Execute a query against the database."""
        try:

            def cb():
                return self._client.query(query, language="sql", mode="polars")

            return self._safe_execute(cb)
        except InfluxDBError as e:
            logger.error(f"Query failed: {str(e)}")
            self._client = None  # Mark client as invalid
            raise
        except Exception as e:
            logger.error(f"Unexpected error during query: {str(e)}")
            self._client = None  # Mark client as invalid
            raise

    def close(self) -> None:
        """Explicitly close the client connection."""
        if self._client is not None:
            try:
                self._client.close()
                logger.info("InfluxDB client closed successfully")
            except Exception as e:
                logger.error(f"Failed to close InfluxDB client: {str(e)}")
            finally:
                self._client = None

    def __del__(self):
        """Ensure the client is closed when the object is destroyed."""
        self.close()
