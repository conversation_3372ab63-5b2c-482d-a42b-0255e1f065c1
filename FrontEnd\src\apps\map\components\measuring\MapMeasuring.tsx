import { useMemo, useEffect, useRef } from "react";
import {
  AdvancedMarker,
  AdvancedMarkerAnchorPoint,
  useMap,
} from "@vis.gl/react-google-maps";
import { useTranslation } from "react-i18next";
import { Ruler, X } from "lucide-react";

import { useMapMeasuring } from "../../hooks/useMapMeasuring";

// Interfaces
export type MapMeasuringProps = ReturnType<typeof useMapMeasuring>;

// Component
export const MeasuringMarker = ({
  position,
  zIndex,
  onDrag,
  onDragStart,
  onDragEnd,
  onClick,
}: {
  position: google.maps.LatLngLiteral;
  zIndex: number;
  onDrag: (ev: google.maps.MapMouseEvent) => void;
  onDragStart: () => void;
  onDragEnd: () => void;
  onClick: () => void;
}) => {
  const { t } = useTranslation("map");

  return (
    <AdvancedMarker
      position={position}
      draggable={true}
      anchorPoint={AdvancedMarkerAnchorPoint.CENTER}
      zIndex={zIndex}
      onDrag={onDrag}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onClick={onClick}
    >
      <button
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-white border-0 outline-0 filter drop-shadow-sm"
        style={{
          boxShadow: "inset 0 0 0 1px #fff, inset 0 0 0 3px #000000",
        }}
        title={t("map.measuring.click_to_remove")}
      />
    </AdvancedMarker>
  );
};

export const MapMeasuring = (props: MapMeasuringProps) => {
  const {
    open,
    distance,
    positions,
    onMarkerClick,
    onMarkerDrag,
    onMarkerDragEnd,
    onMarkerDragStart,
    onClose,
  } = props;

  const { t } = useTranslation("map");
  const map = useMap();
  const polylineRef = useRef<google.maps.Polyline | null>(null);

  // Create and manage polyline
  useEffect(() => {
    if (!map || positions.length < 2 || !open) return;

    // Create polyline if it doesn't exist
    if (!polylineRef.current) {
      polylineRef.current = new google.maps.Polyline({
        path: positions,
        strokeColor: "#000000",
        strokeWeight: 2,
        strokeOpacity: 1,
        zIndex: 2000,
        map,
      });
    } else {
      // Update existing polyline
      polylineRef.current.setPath(positions);
    }

    // Cleanup
    return () => {
      if (polylineRef.current) {
        polylineRef.current.setMap(null);
        polylineRef.current = null;
      }
    };
  }, [map, positions, open]);

  // Render markers
  const markers = useMemo(() => {
    const handleDrag = (index: number) => (ev: google.maps.MapMouseEvent) => {
      onMarkerDrag(ev, index);
    };

    const handleClick = (index: number) => () => {
      onMarkerClick(index);
    };

    return positions.map((item, index) => {
      const zIndex = 2001 + index;

      return (
        <MeasuringMarker
          key={index}
          position={item}
          zIndex={zIndex}
          onDrag={handleDrag(index)}
          onDragStart={onMarkerDragStart}
          onDragEnd={onMarkerDragEnd}
          onClick={handleClick(index)}
        />
      );
    });
  }, [
    positions,
    onMarkerDrag,
    onMarkerClick,
    onMarkerDragStart,
    onMarkerDragEnd,
  ]);

  if (!open) {
    return null;
  }

  // Format distance from meters to kilometers with 2 decimal places
  const formattedDistance =
    typeof distance === "number" ? (distance / 1000).toFixed(2) : undefined;

  return (
    <>
      {markers}

      <div className="fixed bottom-6 left-1/2 -translate-x-1/2 max-w-[260px] w-full z-50">
        <div className="bg-[#212121] text-white rounded-md shadow-md p-2 mx-2.5 flex items-center justify-between gap-3 transition-all duration-200">
          <div className="flex items-center px-1">
            <Ruler size={18} className="text-white" />
          </div>
          <div className="flex-1 pl-1">
            <p className="m-0 font-medium text-sm">
              {t("map.measuring.distance_label")}{" "}
              {formattedDistance ? (
                <>
                  <strong>{formattedDistance}</strong> {t("map.measuring.km")}
                </>
              ) : (
                "--"
              )}
            </p>
            {!formattedDistance && (
              <p className="mt-1 mb-0 text-xs text-gray-300 leading-tight">
                {t("map.measuring.select_points_hint")}
              </p>
            )}
          </div>
          <button
            onClick={onClose}
            aria-label={t("close")}
            className="flex items-center justify-center bg-transparent border-none rounded p-1 cursor-pointer text-white hover:bg-[#333] focus:outline-none"
          >
            <X size={16} />
          </button>
        </div>
      </div>
    </>
  );
};
