## Project Settings

## Technology

### Uv
Install [Uv](https://github.com/astral-sh/uv)
```bash
pip install uv
```

## <PERSON><PERSON><PERSON> Orm
Read the docs [Docs](https://piccolo-orm.readthedocs.io/en/latest/piccolo/migrations/create.html)

Piccolo included the ORM, migrations and admin panel.

## Getting Started
start app

```bash
uv run main.py

# Or
source .venv/bin/activate
python main.py
```

### Commands
Create an admin

```
piccolo user create --username=admin --password=admin12 --email=<EMAIL>  --is_admin=t --is_superuser=t --is_active=t
```

### Routes and Documentation

* Documentation - http://127.0.0.1:8080/docs
* Mqtt Documentation - http://127.0.0.1:8080/api/mqtt/docs



