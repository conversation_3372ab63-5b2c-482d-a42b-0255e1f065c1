from __future__ import annotations

from typing import Annotated

from fastapi import Depends, HTTPException, Security
from fastapi_jwt import JwtAuthorizationCredentials

from .jwt import access_security, user_from_credentials
from .models.user import UserDB


async def current_user(
    auth: JwtAuthorizationCredentials = Security(access_security),
) -> UserDB:
    """Return the current authorized user."""
    if not auth:
        raise HTTPException(401, "No authorization credentials found")
    user = await user_from_credentials(auth)
    if user is None:
        raise HTTPException(404, "Authorized user could not be found")
    return user


async def current_admin(user: UserDep) -> UserDB:
    if user.role not in ["admin", "developer"]:
        raise HTTPException(403, "Not enough permissions")
    return user


UserDep = Annotated[UserDB, Depends(current_user)]
AdminDep = Annotated[UserDB, Depends(current_admin)]
