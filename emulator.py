import asyncio
import enum
import random
import subprocess
import threading
import time
from dataclasses import dataclass
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field
from pydantic_settings import BaseSettings

from backend.src.conf.settings import settings
from backend.src.mqtt import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, auto_load_handlers


class ActivityState(enum.IntEnum):
    IDLE = 0  # Ready but not doing anything
    ACTIVE = 1
    PENDING = 2
    FAILED = 3


class SweepParams(BaseModel):
    time: float
    params: list


class TransmissionBase(BaseModel):
    sdr_type: str
    timer: int = 360
    sweep_params: Optional[SweepParams] = None
    file_link: Optional[str] = None


class StartTransmissionData(TransmissionBase):
    _id: str  # SDR id
    event_id: int | str  # Event id
    signal_param: dict

    bit_rate: int = 1024
    sample_rate: float = 40e6
    num_bits_per_sample: int = 1024
    gain: int = 1
    range_start: Optional[int] = None
    range_stop: Optional[int] = None
    preset_name: Optional[str] = None


class WithStatus(BaseModel):

    model_config = ConfigDict(arbitrary_types_allowed=True)

    status: ActivityState = ActivityState.IDLE
    after_set_idle: Optional[callable] = Field(default=None, exclude=True)
    after_set_active: Optional[callable] = Field(default=None, exclude=True)
    after_set_pending: Optional[callable] = Field(default=None, exclude=True)
    after_set_error: Optional[callable] = Field(default=None, exclude=True)

    def register_callbacks(
        self,
        after_set_idle: Optional[callable] = None,
        after_set_active: Optional[callable] = None,
        after_set_pending: Optional[callable] = None,
        after_set_error: Optional[callable] = None,
    ):
        self.after_set_idle = after_set_idle
        self.after_set_active = after_set_active
        self.after_set_pending = after_set_pending
        self.after_set_error = after_set_error

    def is_active(self) -> bool:
        return self.status == ActivityState.ACTIVE

    def set_idle(self) -> None:
        self.status = ActivityState.IDLE
        print("Set idle")
        if self.after_set_idle:
            self.after_set_idle()

    def set_active(self) -> None:
        self.status = ActivityState.ACTIVE
        print("Set active")
        if self.after_set_active:
            self.after_set_active()

    def set_pending(self) -> None:
        self.status = ActivityState.PENDING
        print("Set pending")
        if self.after_set_pending:
            self.after_set_pending()

    def set_error(self) -> None:
        self.status = ActivityState.ERROR
        print("Set error")
        if self.after_set_error:
            self.after_set_error()


class SDRState(StartTransmissionData, WithStatus):
    ip: Optional[str] = None
    event_id: Optional[int] = None
    start_time: Optional[int] = None
    signal_param: dict = Field(default_factory=dict)
    sweep_params: Optional[SweepParams] = None
    status: ActivityState = ActivityState.IDLE


class Switcher(WithStatus):
    base_url: str
    led: str
    freq_range: str

    def switch(self) -> int:
        if self.status == ActivityState.FAILED:
            print("Switcher is in error state")
            return -1
        if self.is_active():
            self.set_idle()
        else:
            self.set_active()
        return self.status

    def get_state(self) -> dict:
        return {
            "led": self.led,
            "freq_range": self.freq_range,
            "status": self.status,
        }


class RelayManager:

    def __init__(self, noise_data: dict, relay_ip: str):
        self.base_url = f"http://{relay_ip}/protect"
        self.noise_data = noise_data
        self.switchers = [
            Switcher(led=led, freq_range=freq_range, base_url=self.base_url)
            for led, freq_range in noise_data.items()
        ]

    def switch(self, led: str) -> bool:
        for s in self.switchers:
            if s.led == led:
                return s.switch()

    def get_status(self) -> List[dict[int, bool]]:
        return [s.get_state() for s in self.switchers]


@dataclass
class Monitor:
    labels = {0: "1", 1: "2"}

    def get_temperatures(self) -> dict:
        amplifier = random.randint(0, 100)
        sensors_by_floor = {
            "1": random.randint(0, 100),
            "2": random.randint(0, 100),
        }

        return {"amplifier": amplifier, "sensors_by_floor": sensors_by_floor}


class Config(BaseSettings):

    relay_ip: str = "************"

    is_exist_temp_amplifier: bool = True
    is_rasp: bool = False

    mqtt_broker_host: str = "localhost"
    mqtt_username: Optional[str] = None
    mqtt_password: Optional[str] = None
    mqtt_broker_port: int = 1883
    mqtt_prefix: str = "dev"

    state_update_interval: int = 5

    model_config = ConfigDict(extra="allow")


config = Config()


class SDR:
    """
    Represent a SDR. Can start and stop transmission.
    Keep its state in a SDRState
    """

    def __init__(self, name: str, data: dict):
        data["sdr_type"] = name
        self.state = SDRState(**data)
        self.state.register_callbacks(after_set_pending=push_state)
        self.time_finish = None

    def activate(self) -> None:
        """Start SDR transmission with the current configuration."""
        self.state.set_pending()
        time.sleep(5)
        self.time_finish = int(time.time() + self.state.timer)
        self.state.start_time = int(time.time())
        # NOTE: after start set sdr as active
        self.state.set_active()

    def reset(self):
        print(f"Reset sdr: {self.state.sdr_type}")
        self.state.set_pending()
        time.sleep(5)
        self.state.set_idle()
        self.state.timer = None
        self.state.start_time = None
        self.time_finish = None

    def get_state(self):
        data = self.state.model_dump()
        if self.state.is_active() and self.time_finish:
            time_left = self.time_finish - int(time.time())
            if time_left < 0:
                self.reset()
                return self.get_state()
            data["time_left"] = time_left
        return data


class Controller:
    def __init__(self):
        self.sdrs: dict[str, SDR] = {}
        self.monitor = Monitor()
        self.relay: Optional[RelayManager] = None  # Register when insert initial state

    def insert_initial_state(self, data: dict):
        self._register_sdrs(data["sdrs"])
        if noise_data := data.get("relay_leds"):
            self.relay = RelayManager(
                noise_data=noise_data,
                relay_ip=config.relay_ip,
            )

    def _register_sdrs(self, sdrs: dict):
        for name, data in sdrs.items():
            self.sdrs[name] = SDR(name, data)

    def activate_sdr(self, data: StartTransmissionData):
        # Stop previous transmission if any
        if sdr := self.sdrs.get(data.sdr_type):
            if sdr.state.is_active():
                sdr.reset()
        sdr = SDR(data.sdr_type, data.model_dump())
        self.sdrs[data.sdr_type] = sdr  # Update first

        # Then activate (so callbacks see the latest state)
        sdr.activate()

    def stop_sdr(self, name: str):
        if sdr := self.sdrs.get(name):
            sdr.reset()

    def get_state(self):
        if not self.sdrs:
            return {}
        sdrs = {name: sdr.get_state() for name, sdr in self.sdrs.items()}
        temp_data = self.monitor.get_temperatures()

        relay_data = self.relay.get_status() if self.relay else None

        data = {
            "relay": relay_data,
            "temp": temp_data,
            "sdrs": sdrs,
        }
        return data

    def switch_relay_state(self, led: str):
        if self.relay:
            self.relay.switch(led)


controller = Controller()


#####


RELAY_IP = "*************"
IS_EXIST_TEMP_AMPLIFIER = False
IS_RASP: bool = False
RASPBERRY_ID = input("Enter raspberry id: ")
# RASPBERRY_ID = "1"


def on_start(sdr_data: StartTransmissionData):
    print("Start command with data:", sdr_data)

    def start():
        controller.activate_sdr(sdr_data)
        return push_state()

    threading.Thread(target=start).start()
    mqtt.publish(
        "events",
        {
            "event": "start",
            "event_id": sdr_data.event_id,
        },
        raspberry_id=RASPBERRY_ID,
    )


def on_start_signal_file(sdr_data: dict):
    print("Start command with data:", sdr_data)
    controller.activate_sdr(sdr_data)
    return push_state()


def on_stop(sdr_data: TransmissionBase):
    print("On stop ", sdr_data)
    event_id = None
    if sdr := controller.sdrs.get(sdr_data.sdr_type):
        event_id = sdr.state.event_id

    def stop():
        controller.stop_sdr(sdr_data.sdr_type)
        return push_state()

    threading.Thread(target=stop).start()
    mqtt.publish(
        "events",
        {
            "event": "stop",
            "event_id": event_id,
        },
        raspberry_id=RASPBERRY_ID,
    )


def on_switch_relay(data: dict):
    controller.switch_relay_state(data["led"])
    return push_state()


def on_insert_initial_state(data: dict):
    print("on_insert_initial_state ", data)
    controller.insert_initial_state(data)
    push_state()


def push_state(empty=False):
    content = {} if empty else controller.get_state()
    mqtt.publish("status", content, raspberry_id=RASPBERRY_ID)


def on_execute_cmd(data: dict):
    cmd = data["cmd"].split(" ")
    print(f"Try to execute cmd: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    print(f"cmd result: {result.stdout}")
    return result.stdout


def on_server_restarted(data: dict):
    """Load latest data when server restarted"""
    mqtt.on_connect_callback()


async def state_pusher():
    print("Start state pusher")
    while True:
        push_state()
        await asyncio.sleep(config.state_update_interval)


async def main():

    print("Connecting to MQTT broker")

    auto_load_handlers(mqtt, globals().items())
    print("Handlers loaded")

    mqtt.on_connect_callback = lambda: mqtt.publish(
        "connect_group", {}, raspberry_id=RASPBERRY_ID
    )

    await mqtt.connect()

    try:
        # Keep the event loop running indefinitely
        await state_pusher()
        print("Start infinite loop")
        await asyncio.Event().wait()
    finally:
        push_state(empty=True)
        await mqtt.disconnect()


mqtt = MQTTManager(
    host=settings.mqtt_broker_host,
    port=settings.mqtt_broker_port,
    username=settings.mqtt_username,
    password=settings.mqtt_password,
    raspberry_id=RASPBERRY_ID,
    prefix=settings.mqtt_prefix,
)


asyncio.run(main())
