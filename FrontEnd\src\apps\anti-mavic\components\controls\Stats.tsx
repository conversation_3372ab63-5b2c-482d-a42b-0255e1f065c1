import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import * as Progress from "@radix-ui/react-progress";
import { BatteryMedium, ZapIcon, Thermometer } from "lucide-react";
import * as Switch from "@radix-ui/react-switch";

const Stats = () => {
  const { t } = useTranslation("antimavic");
  const [coolingStatus, setCoolingStatus] = useState(false);

  // Hardcoded stats for now
  const batteryLevel = 78;
  const batteryTimeRemaining = "4h 35 min";
  const voltage = "12.6 V";
  const current = "3.2 A";
  const power = "40.3W";
  const temperature = 83;

  return (
    <div className="w-full space-y-3 text-sm">
      {/* Battery Level */}
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <BatteryMedium size={18} className="text-[--green-5]" />
            <h3 className="font-medium text-[--gray-1]">
              {t("controls.battery.level")}
            </h3>
          </div>
          <span className="text-xs text-[--gray-1]">
            {batteryLevel}% {t("controls.battery.remaining")}
          </span>
        </div>

        <Progress.Root
          className="relative overflow-hidden bg-[--gray-7] rounded-full w-full h-[5px]"
          value={batteryLevel}
        >
          <Progress.Indicator
            className="bg-[--green-5] w-full h-full transition-transform duration-300"
            style={{ transform: `translateX(-${100 - batteryLevel}%)` }}
          />
        </Progress.Root>

        <div className="text-xs text-[--gray-4]">
          {t("controls.battery.estimatedTime")}: {batteryTimeRemaining}
        </div>
      </div>

      {/* Current Consumption */}
      <div className="space-y-1">
        <div className="flex items-center gap-1">
          <ZapIcon size={18} className="text-[--green-5]" />
          <h3 className="font-medium text-[--gray-1]">
            {t("controls.consumption.title")}
          </h3>
        </div>

        <div className="grid grid-cols-3 gap-2 text-center pt-1">
          <div>
            <div className="text-xs text-[--gray-4]">
              {t("controls.consumption.voltage")}
            </div>
            <div className="font-medium text-[--gray-1]">{voltage}</div>
          </div>
          <div>
            <div className="text-xs text-[--gray-4]">
              {t("controls.consumption.current")}
            </div>
            <div className="font-medium text-[--gray-1]">{current}</div>
          </div>
          <div>
            <div className="text-xs text-[--gray-4]">
              {t("controls.consumption.power")}
            </div>
            <div className="font-medium text-[--gray-1]">{power}</div>
          </div>
        </div>
      </div>

      {/* Temperature & Cooling Status */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Thermometer size={18} className="text-[--blue-5]" />
            <h3 className="font-medium text-[--gray-1]">
              {t("controls.temperature.title")}
            </h3>
          </div>
          <span className="text-xs text-[--gray-1]">
            {temperature}°C{" "}
            <span className="text-[--gray-4]">
              {t("controls.temperature.normal")}
            </span>
          </span>
        </div>
        <div className="flex items-center justify-between pt-1">
          <label
            htmlFor="cooling-status-stats"
            className="text-xs text-[--gray-1]"
          >
            {t("controls.settings.coolingStatus")}
          </label>
          <Switch.Root
            id="cooling-status-stats"
            checked={coolingStatus}
            onCheckedChange={setCoolingStatus}
            className={`w-[38px] h-[22px] rounded-full relative ${coolingStatus ? "bg-[--green-5]" : "bg-[--gray-7]"} transition-colors`}
          >
            <Switch.Thumb className="block w-[18px] h-[18px] bg-white rounded-full transition-transform duration-100 will-change-transform translate-x-0.5 data-[state=checked]:translate-x-[17px]" />
          </Switch.Root>
        </div>
      </div>
    </div>
  );
};

export default Stats;
